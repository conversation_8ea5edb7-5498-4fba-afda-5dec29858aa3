{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './Header.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const scrollToSection = sectionId => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth'\n      });\n      setIsMobileMenuOpen(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `header ${isScrolled ? 'scrolled' : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: `nav ${isMobileMenuOpen ? 'nav-open' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: portfolioConfig.navigation.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => scrollToSection(item.id),\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        className: \"mobile-menu-toggle\",\n        onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n        \"aria-label\": \"Toggle mobile menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"0+zEKVBL95ILuBb5rHE6ViYOHu8=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "portfolioConfig", "jsxDEV", "_jsxDEV", "Header", "_s", "isScrolled", "setIsScrolled", "isMobileMenuOpen", "setIsMobileMenuOpen", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "navigation", "map", "item", "type", "onClick", "id", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Header.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Header.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\nconst Header: React.FC = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      setIsMobileMenuOpen(false);\n    }\n  };\n\n  return (\n    <header className={`header ${isScrolled ? 'scrolled' : ''}`}>\n      <div className=\"header-container\">\n        <div className=\"logo\">\n          <h2>Portfolio</h2>\n        </div>\n        \n        <nav className={`nav ${isMobileMenuOpen ? 'nav-open' : ''}`}>\n          <ul>\n            {portfolioConfig.navigation.map((item) => (\n              <li key={item.id}>\n                <button type=\"button\" onClick={() => scrollToSection(item.id)}>\n                  {item.name}\n                </button>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        <button\n          type=\"button\"\n          className=\"mobile-menu-toggle\"\n          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          aria-label=\"Toggle mobile menu\"\n        >\n          <span></span>\n          <span></span>\n          <span></span>\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,cAAc;AACrB,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACd,MAAMU,YAAY,GAAGA,CAAA,KAAM;MACzBH,aAAa,CAACI,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAIC,SAAiB,IAAK;IAC7C,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;MAC9CZ,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,oBACEN,OAAA;IAAQmB,SAAS,EAAE,UAAUhB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;IAAAiB,QAAA,eAC1DpB,OAAA;MAAKmB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpB,OAAA;QAAKmB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpB,OAAA;UAAAoB,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAENxB,OAAA;QAAKmB,SAAS,EAAE,OAAOd,gBAAgB,GAAG,UAAU,GAAG,EAAE,EAAG;QAAAe,QAAA,eAC1DpB,OAAA;UAAAoB,QAAA,EACGtB,eAAe,CAAC2B,UAAU,CAACC,GAAG,CAAEC,IAAI,iBACnC3B,OAAA;YAAAoB,QAAA,eACEpB,OAAA;cAAQ4B,IAAI,EAAC,QAAQ;cAACC,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAACe,IAAI,CAACG,EAAE,CAAE;cAAAV,QAAA,EAC3DO,IAAI,CAACI;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GAHFG,IAAI,CAACG,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIZ,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENxB,OAAA;QACE4B,IAAI,EAAC,QAAQ;QACbT,SAAS,EAAC,oBAAoB;QAC9BU,OAAO,EAAEA,CAAA,KAAMvB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;QACtD,cAAW,oBAAoB;QAAAe,QAAA,gBAE/BpB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACtB,EAAA,CArDID,MAAgB;AAAA+B,EAAA,GAAhB/B,MAAgB;AAuDtB,eAAeA,MAAM;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}