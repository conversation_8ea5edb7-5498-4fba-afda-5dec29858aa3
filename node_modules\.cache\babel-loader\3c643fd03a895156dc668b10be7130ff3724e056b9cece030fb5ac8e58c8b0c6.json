{"ast": null, "code": "import React from'react';import'./Experience.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Experience=()=>{const experiences=portfolioConfig.experience||[];return/*#__PURE__*/_jsx(\"section\",{id:\"experience\",className:\"experience\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"Professional Experience\"}),/*#__PURE__*/_jsx(\"div\",{className:\"experience-timeline\",children:experiences.map((exp,index)=>/*#__PURE__*/_jsxs(\"div\",{className:`experience-item ${index%2===0?'left':'right'}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"experience-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"experience-header\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"position\",children:exp.position}),/*#__PURE__*/_jsxs(\"div\",{className:\"company-info\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"company\",children:exp.company}),/*#__PURE__*/_jsx(\"span\",{className:\"location\",children:exp.location})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"duration-type\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"duration\",children:exp.duration}),/*#__PURE__*/_jsx(\"span\",{className:`type type-${exp.type}`,children:exp.type})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"experience-description\",children:/*#__PURE__*/_jsx(\"ul\",{children:exp.description.map((item,idx)=>/*#__PURE__*/_jsx(\"li\",{children:item},idx))})}),/*#__PURE__*/_jsxs(\"div\",{className:\"experience-technologies\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Technologies Used:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"tech-tags\",children:exp.technologies.map((tech,idx)=>/*#__PURE__*/_jsx(\"span\",{className:\"tech-tag\",children:tech},idx))})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"experience-marker\",children:/*#__PURE__*/_jsx(\"div\",{className:\"marker-dot\"})})]},exp.id))})]})});};export default Experience;", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Experience", "experiences", "experience", "id", "className", "children", "map", "exp", "index", "position", "company", "location", "duration", "type", "description", "item", "idx", "technologies", "tech"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Experience.tsx"], "sourcesContent": ["import React from 'react';\nimport './Experience.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface ExperienceItem {\n  id: number;\n  company: string;\n  position: string;\n  duration: string;\n  location: string;\n  description: string[];\n  technologies: string[];\n  type: 'full-time' | 'part-time' | 'contract' | 'internship';\n}\n\nconst Experience: React.FC = () => {\n  const experiences: ExperienceItem[] = portfolioConfig.experience || [];\n\n  return (\n    <section id=\"experience\" className=\"experience\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Professional Experience</h2>\n        <div className=\"experience-timeline\">\n          {experiences.map((exp, index) => (\n            <div key={exp.id} className={`experience-item ${index % 2 === 0 ? 'left' : 'right'}`}>\n              <div className=\"experience-content\">\n                <div className=\"experience-header\">\n                  <h3 className=\"position\">{exp.position}</h3>\n                  <div className=\"company-info\">\n                    <h4 className=\"company\">{exp.company}</h4>\n                    <span className=\"location\">{exp.location}</span>\n                  </div>\n                  <div className=\"duration-type\">\n                    <span className=\"duration\">{exp.duration}</span>\n                    <span className={`type type-${exp.type}`}>{exp.type}</span>\n                  </div>\n                </div>\n                \n                <div className=\"experience-description\">\n                  <ul>\n                    {exp.description.map((item, idx) => (\n                      <li key={idx}>{item}</li>\n                    ))}\n                  </ul>\n                </div>\n\n                <div className=\"experience-technologies\">\n                  <h5>Technologies Used:</h5>\n                  <div className=\"tech-tags\">\n                    {exp.technologies.map((tech, idx) => (\n                      <span key={idx} className=\"tech-tag\">{tech}</span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n              <div className=\"experience-marker\">\n                <div className=\"marker-dot\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,kBAAkB,CACzB,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAa5D,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,WAA6B,CAAGN,eAAe,CAACO,UAAU,EAAI,EAAE,CAEtE,mBACEL,IAAA,YAASM,EAAE,CAAC,YAAY,CAACC,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC7CN,KAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBR,IAAA,OAAIO,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAC1DR,IAAA,QAAKO,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCJ,WAAW,CAACK,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,gBAC1BT,KAAA,QAAkBK,SAAS,CAAE,mBAAmBI,KAAK,CAAG,CAAC,GAAK,CAAC,CAAG,MAAM,CAAG,OAAO,EAAG,CAAAH,QAAA,eACnFN,KAAA,QAAKK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCN,KAAA,QAAKK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCR,IAAA,OAAIO,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAEE,GAAG,CAACE,QAAQ,CAAK,CAAC,cAC5CV,KAAA,QAAKK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BR,IAAA,OAAIO,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEE,GAAG,CAACG,OAAO,CAAK,CAAC,cAC1Cb,IAAA,SAAMO,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAEE,GAAG,CAACI,QAAQ,CAAO,CAAC,EAC7C,CAAC,cACNZ,KAAA,QAAKK,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BR,IAAA,SAAMO,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAEE,GAAG,CAACK,QAAQ,CAAO,CAAC,cAChDf,IAAA,SAAMO,SAAS,CAAE,aAAaG,GAAG,CAACM,IAAI,EAAG,CAAAR,QAAA,CAAEE,GAAG,CAACM,IAAI,CAAO,CAAC,EACxD,CAAC,EACH,CAAC,cAENhB,IAAA,QAAKO,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCR,IAAA,OAAAQ,QAAA,CACGE,GAAG,CAACO,WAAW,CAACR,GAAG,CAAC,CAACS,IAAI,CAAEC,GAAG,gBAC7BnB,IAAA,OAAAQ,QAAA,CAAeU,IAAI,EAAVC,GAAe,CACzB,CAAC,CACA,CAAC,CACF,CAAC,cAENjB,KAAA,QAAKK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCR,IAAA,OAAAQ,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BR,IAAA,QAAKO,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBE,GAAG,CAACU,YAAY,CAACX,GAAG,CAAC,CAACY,IAAI,CAAEF,GAAG,gBAC9BnB,IAAA,SAAgBO,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAEa,IAAI,EAA/BF,GAAsC,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cACNnB,IAAA,QAAKO,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCR,IAAA,QAAKO,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,GAjCEG,GAAG,CAACJ,EAkCT,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}