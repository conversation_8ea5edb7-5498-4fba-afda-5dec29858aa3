{"ast": null, "code": "import React,{useState}from'react';import'./Projects.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Projects=()=>{const[activeFilter,setActiveFilter]=useState('All');const projects=portfolioConfig.projects;const categories=['All','Web App','Frontend','Mobile'];const filteredProjects=activeFilter==='All'?projects:projects.filter(project=>project.category===activeFilter);return/*#__PURE__*/_jsx(\"section\",{id:\"projects\",className:\"projects\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"Featured Projects\"}),/*#__PURE__*/_jsx(\"div\",{className:\"project-filters\",children:categories.map(category=>/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:`filter-btn ${activeFilter===category?'active':''}`,onClick:()=>setActiveFilter(category),children:category},category))}),/*#__PURE__*/_jsx(\"div\",{className:\"projects-grid\",children:filteredProjects.map(project=>/*#__PURE__*/_jsxs(\"div\",{className:\"project-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"project-image\",children:/*#__PURE__*/_jsx(\"span\",{className:\"project-emoji\",children:project.image})}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-content\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"project-title\",children:project.title}),/*#__PURE__*/_jsx(\"p\",{className:\"project-description\",children:project.description}),/*#__PURE__*/_jsx(\"div\",{className:\"project-technologies\",children:project.technologies.map(tech=>/*#__PURE__*/_jsx(\"span\",{className:\"tech-tag\",children:tech},tech))}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-links\",children:[project.liveUrl&&/*#__PURE__*/_jsx(\"a\",{href:project.liveUrl,className:\"project-link\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"Live Demo\"}),project.githubUrl&&/*#__PURE__*/_jsx(\"a\",{href:project.githubUrl,className:\"project-link\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"GitHub\"})]})]})]},project.id))})]})});};export default Projects;", "map": {"version": 3, "names": ["React", "useState", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Projects", "activeFilter", "setActiveFilter", "projects", "categories", "filteredProjects", "filter", "project", "category", "id", "className", "children", "map", "type", "onClick", "image", "title", "description", "technologies", "tech", "liveUrl", "href", "target", "rel", "githubUrl"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Projects.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Projects.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface Project {\n  id: number;\n  title: string;\n  description: string;\n  technologies: string[];\n  image: string;\n  liveUrl?: string;\n  githubUrl?: string;\n  category: string;\n}\n\nconst Projects: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('All');\n\n  const projects: Project[] = portfolioConfig.projects;\n\n  const categories = ['All', 'Web App', 'Frontend', 'Mobile'];\n\n  const filteredProjects = activeFilter === 'All' \n    ? projects \n    : projects.filter(project => project.category === activeFilter);\n\n  return (\n    <section id=\"projects\" className=\"projects\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Featured Projects</h2>\n        \n        <div className=\"project-filters\">\n          {categories.map((category) => (\n            <button\n              key={category}\n              type=\"button\"\n              className={`filter-btn ${activeFilter === category ? 'active' : ''}`}\n              onClick={() => setActiveFilter(category)}\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"projects-grid\">\n          {filteredProjects.map((project) => (\n            <div key={project.id} className=\"project-card\">\n              <div className=\"project-image\">\n                <span className=\"project-emoji\">{project.image}</span>\n              </div>\n              <div className=\"project-content\">\n                <h3 className=\"project-title\">{project.title}</h3>\n                <p className=\"project-description\">{project.description}</p>\n                <div className=\"project-technologies\">\n                  {project.technologies.map((tech) => (\n                    <span key={tech} className=\"tech-tag\">{tech}</span>\n                  ))}\n                </div>\n                <div className=\"project-links\">\n                  {project.liveUrl && (\n                    <a href={project.liveUrl} className=\"project-link\" target=\"_blank\" rel=\"noopener noreferrer\">\n                      Live Demo\n                    </a>\n                  )}\n                  {project.githubUrl && (\n                    <a href={project.githubUrl} className=\"project-link\" target=\"_blank\" rel=\"noopener noreferrer\">\n                      GitHub\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,gBAAgB,CACvB,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAa5D,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGR,QAAQ,CAAC,KAAK,CAAC,CAEvD,KAAM,CAAAS,QAAmB,CAAGR,eAAe,CAACQ,QAAQ,CAEpD,KAAM,CAAAC,UAAU,CAAG,CAAC,KAAK,CAAE,SAAS,CAAE,UAAU,CAAE,QAAQ,CAAC,CAE3D,KAAM,CAAAC,gBAAgB,CAAGJ,YAAY,GAAK,KAAK,CAC3CE,QAAQ,CACRA,QAAQ,CAACG,MAAM,CAACC,OAAO,EAAIA,OAAO,CAACC,QAAQ,GAAKP,YAAY,CAAC,CAEjE,mBACEJ,IAAA,YAASY,EAAE,CAAC,UAAU,CAACC,SAAS,CAAC,UAAU,CAAAC,QAAA,cACzCZ,KAAA,QAAKW,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBd,IAAA,OAAIa,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAEpDd,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BP,UAAU,CAACQ,GAAG,CAAEJ,QAAQ,eACvBX,IAAA,WAEEgB,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAE,cAAcT,YAAY,GAAKO,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CACrEM,OAAO,CAAEA,CAAA,GAAMZ,eAAe,CAACM,QAAQ,CAAE,CAAAG,QAAA,CAExCH,QAAQ,EALJA,QAMC,CACT,CAAC,CACC,CAAC,cAENX,IAAA,QAAKa,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BN,gBAAgB,CAACO,GAAG,CAAEL,OAAO,eAC5BR,KAAA,QAAsBW,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5Cd,IAAA,QAAKa,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5Bd,IAAA,SAAMa,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEJ,OAAO,CAACQ,KAAK,CAAO,CAAC,CACnD,CAAC,cACNhB,KAAA,QAAKW,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9Bd,IAAA,OAAIa,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEJ,OAAO,CAACS,KAAK,CAAK,CAAC,cAClDnB,IAAA,MAAGa,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEJ,OAAO,CAACU,WAAW,CAAI,CAAC,cAC5DpB,IAAA,QAAKa,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCJ,OAAO,CAACW,YAAY,CAACN,GAAG,CAAEO,IAAI,eAC7BtB,IAAA,SAAiBa,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAEQ,IAAI,EAAhCA,IAAuC,CACnD,CAAC,CACC,CAAC,cACNpB,KAAA,QAAKW,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BJ,OAAO,CAACa,OAAO,eACdvB,IAAA,MAAGwB,IAAI,CAAEd,OAAO,CAACa,OAAQ,CAACV,SAAS,CAAC,cAAc,CAACY,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAAAZ,QAAA,CAAC,WAE7F,CAAG,CACJ,CACAJ,OAAO,CAACiB,SAAS,eAChB3B,IAAA,MAAGwB,IAAI,CAAEd,OAAO,CAACiB,SAAU,CAACd,SAAS,CAAC,cAAc,CAACY,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAAAZ,QAAA,CAAC,QAE/F,CAAG,CACJ,EACE,CAAC,EACH,CAAC,GAxBEJ,OAAO,CAACE,EAyBb,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}