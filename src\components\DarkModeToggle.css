.dark-mode-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 2rem;
  transition: all 0.3s ease;
  color: var(--text-primary);
}

.dark-mode-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
}

.dark-mode-toggle:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.toggle-track {
  width: 50px;
  height: 26px;
  background: #e2e8f0;
  border-radius: 13px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark-mode-toggle.dark .toggle-track {
  background: #4a5568;
}

.toggle-thumb {
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode-toggle.dark .toggle-thumb {
  transform: translateX(24px);
  background: #2d3748;
}

.toggle-icon {
  color: #fbbf24;
  transition: all 0.3s ease;
}

.dark-mode-toggle.dark .toggle-icon {
  color: #f7fafc;
}

.toggle-label {
  font-size: 0.875rem;
  font-weight: 500;
  -webkit-user-select: none;
  user-select: none;
}

/* Dark mode global styles */
:root.dark-mode {
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --text-light: #718096;
  
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --bg-tertiary: #4a5568;
  
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:root.dark-mode body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Dark mode component overrides */
:root.dark-mode .header {
  background: rgba(26, 32, 44, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:root.dark-mode .header.scrolled {
  background: rgba(26, 32, 44, 0.98);
  box-shadow: 
    0 4px 32px rgba(0, 0, 0, 0.3),
    0 2px 16px rgba(0, 0, 0, 0.2);
}

:root.dark-mode .hero {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

:root.dark-mode .about {
  background: var(--bg-primary);
}

:root.dark-mode .skills {
  background: var(--bg-secondary);
}

:root.dark-mode .experience {
  background: var(--bg-primary);
}

:root.dark-mode .certificates {
  background: var(--bg-secondary);
}

:root.dark-mode .projects {
  background: var(--bg-primary);
}

:root.dark-mode .contact {
  background: var(--primary-gradient);
}

/* Dark mode card styles */
:root.dark-mode .skill-category,
:root.dark-mode .experience-content,
:root.dark-mode .certificate-card,
:root.dark-mode .project-card {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

:root.dark-mode .skill-category:hover,
:root.dark-mode .experience-content:hover,
:root.dark-mode .certificate-card:hover,
:root.dark-mode .project-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Dark mode form styles */
:root.dark-mode .contact-form {
  background: rgba(45, 55, 72, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:root.dark-mode .form-group input,
:root.dark-mode .form-group textarea {
  background: rgba(45, 55, 72, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

:root.dark-mode .form-group input:focus,
:root.dark-mode .form-group textarea:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(45, 55, 72, 0.7);
}

/* Dark mode accessibility panel */
:root.dark-mode .accessibility-panel {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

:root.dark-mode .accessibility-group h4 {
  color: var(--text-primary);
}

:root.dark-mode .font-controls button {
  background: var(--bg-tertiary);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

:root.dark-mode .font-size-display {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

:root.dark-mode .checkmark {
  background: var(--bg-tertiary);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Dark mode loading screen */
:root.dark-mode .loading-screen {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Mobile styles */
@media (max-width: 768px) {
  .dark-mode-toggle {
    gap: 0.5rem;
  }
  
  .toggle-track {
    width: 44px;
    height: 24px;
  }
  
  .toggle-thumb {
    width: 20px;
    height: 20px;
  }
  
  .dark-mode-toggle.dark .toggle-thumb {
    transform: translateX(20px);
  }
  
  .toggle-label {
    font-size: 0.8rem;
  }
}
