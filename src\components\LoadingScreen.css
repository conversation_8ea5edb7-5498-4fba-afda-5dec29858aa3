.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.8s ease, visibility 0.8s ease;
}

.loading-screen.fade-out {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.loading-logo h1 {
  font-family: 'Playfair Display', serif;
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.logo-underline {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #ffd89b, #19547b);
  margin: 0 auto 3rem;
  border-radius: 2px;
  animation: underlineExpand 2s ease-in-out infinite;
}

.loading-progress {
  margin-bottom: 2rem;
}

.progress-bar {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin: 0 auto 1rem;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffd89b, #19547b);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShimmer 1.5s ease-in-out infinite;
}

.progress-text {
  font-family: 'JetBrains Mono', monospace;
  font-size: 1.2rem;
  font-weight: 600;
  opacity: 0.9;
}

.loading-subtitle {
  font-size: 1.1rem;
  font-weight: 300;
  opacity: 0.8;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  animation: subtitleFade 3s ease-in-out infinite;
}

.loading-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s linear infinite;
}

.particle-1 { left: 10%; animation-delay: 0s; }
.particle-2 { left: 20%; animation-delay: 0.5s; }
.particle-3 { left: 30%; animation-delay: 1s; }
.particle-4 { left: 40%; animation-delay: 1.5s; }
.particle-5 { left: 50%; animation-delay: 2s; }
.particle-6 { left: 60%; animation-delay: 2.5s; }
.particle-7 { left: 70%; animation-delay: 3s; }
.particle-8 { left: 80%; animation-delay: 3.5s; }
.particle-9 { left: 90%; animation-delay: 4s; }
.particle-10 { left: 15%; animation-delay: 4.5s; }
.particle-11 { left: 25%; animation-delay: 5s; }
.particle-12 { left: 35%; animation-delay: 5.5s; }
.particle-13 { left: 45%; animation-delay: 6s; }
.particle-14 { left: 55%; animation-delay: 6.5s; }
.particle-15 { left: 65%; animation-delay: 7s; }
.particle-16 { left: 75%; animation-delay: 7.5s; }
.particle-17 { left: 85%; animation-delay: 8s; }
.particle-18 { left: 95%; animation-delay: 8.5s; }
.particle-19 { left: 5%; animation-delay: 9s; }
.particle-20 { left: 95%; animation-delay: 9.5s; }

/* Animations */
@keyframes logoGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    transform: scale(1);
  }
  100% {
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
    transform: scale(1.02);
  }
}

@keyframes underlineExpand {
  0%, 100% {
    transform: scaleX(1);
  }
  50% {
    transform: scaleX(1.2);
  }
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes subtitleFade {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .loading-logo h1 {
    font-size: 3rem;
  }
  
  .progress-bar {
    width: 250px;
  }
  
  .loading-subtitle {
    font-size: 1rem;
  }
}
