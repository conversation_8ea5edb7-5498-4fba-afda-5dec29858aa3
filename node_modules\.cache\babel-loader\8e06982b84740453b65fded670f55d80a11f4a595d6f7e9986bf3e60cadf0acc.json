{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\";\nimport React from 'react';\nimport './Hero.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  const scrollToSection = sectionId => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"hero\",\n    className: \"hero\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hero-title\",\n          children: [\"Hi, I'm \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"highlight\",\n            children: portfolioConfig.personal.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"hero-subtitle\",\n          children: portfolioConfig.personal.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-description\",\n          children: portfolioConfig.personal.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-primary\",\n            onClick: () => scrollToSection('projects'),\n            children: \"View My Work\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: () => scrollToSection('contact'),\n            children: \"Get In Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-image\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-avatar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"avatar-placeholder\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: portfolioConfig.personal.avatar\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-scroll-indicator\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-arrow\",\n        onClick: () => scrollToSection('about'),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2193\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsxDEV", "_jsxDEV", "Hero", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "id", "className", "children", "personal", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "type", "onClick", "avatar", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Hero.tsx"], "sourcesContent": ["import React from 'react';\nimport './Hero.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\nconst Hero: React.FC = () => {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"hero\" className=\"hero\">\n      <div className=\"hero-container\">\n        <div className=\"hero-content\">\n          <h1 className=\"hero-title\">\n            Hi, I'm <span className=\"highlight\">{portfolioConfig.personal.name}</span>\n          </h1>\n          <h2 className=\"hero-subtitle\">\n            {portfolioConfig.personal.title}\n          </h2>\n          <p className=\"hero-description\">\n            {portfolioConfig.personal.description}\n          </p>\n          <div className=\"hero-buttons\">\n            <button\n              type=\"button\"\n              className=\"btn btn-primary\"\n              onClick={() => scrollToSection('projects')}\n            >\n              View My Work\n            </button>\n            <button\n              type=\"button\"\n              className=\"btn btn-secondary\"\n              onClick={() => scrollToSection('contact')}\n            >\n              Get In Touch\n            </button>\n          </div>\n        </div>\n        <div className=\"hero-image\">\n          <div className=\"hero-avatar\">\n            <div className=\"avatar-placeholder\">\n              <span>{portfolioConfig.personal.avatar}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"hero-scroll-indicator\">\n        <div className=\"scroll-arrow\" onClick={() => scrollToSection('about')}>\n          <span>↓</span>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AACnB,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,eAAe,GAAIC,SAAiB,IAAK;IAC7C,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChD;EACF,CAAC;EAED,oBACER,OAAA;IAASS,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACjCX,OAAA;MAAKU,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BX,OAAA;QAAKU,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BX,OAAA;UAAIU,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,UACjB,eAAAX,OAAA;YAAMU,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEb,eAAe,CAACc,QAAQ,CAACC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACLjB,OAAA;UAAIU,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1Bb,eAAe,CAACc,QAAQ,CAACM;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACLjB,OAAA;UAAGU,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5Bb,eAAe,CAACc,QAAQ,CAACO;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACJjB,OAAA;UAAKU,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BX,OAAA;YACEoB,IAAI,EAAC,QAAQ;YACbV,SAAS,EAAC,iBAAiB;YAC3BW,OAAO,EAAEA,CAAA,KAAMnB,eAAe,CAAC,UAAU,CAAE;YAAAS,QAAA,EAC5C;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA;YACEoB,IAAI,EAAC,QAAQ;YACbV,SAAS,EAAC,mBAAmB;YAC7BW,OAAO,EAAEA,CAAA,KAAMnB,eAAe,CAAC,SAAS,CAAE;YAAAS,QAAA,EAC3C;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjB,OAAA;QAAKU,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBX,OAAA;UAAKU,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BX,OAAA;YAAKU,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCX,OAAA;cAAAW,QAAA,EAAOb,eAAe,CAACc,QAAQ,CAACU;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjB,OAAA;MAAKU,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCX,OAAA;QAAKU,SAAS,EAAC,cAAc;QAACW,OAAO,EAAEA,CAAA,KAAMnB,eAAe,CAAC,OAAO,CAAE;QAAAS,QAAA,eACpEX,OAAA;UAAAW,QAAA,EAAM;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GArDItB,IAAc;AAuDpB,eAAeA,IAAI;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}