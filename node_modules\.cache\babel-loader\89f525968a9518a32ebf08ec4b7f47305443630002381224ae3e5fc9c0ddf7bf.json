{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./CursorFollower.css';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const CursorFollower=()=>{const[mousePosition,setMousePosition]=useState({x:0,y:0});const[isHovering,setIsHovering]=useState(false);const[isClicking,setIsClicking]=useState(false);useEffect(()=>{const handleMouseMove=e=>{setMousePosition({x:e.clientX,y:e.clientY});};const handleMouseEnter=e=>{const target=e.target;if(target.matches('button, a, .clickable, input, textarea')){setIsHovering(true);}};const handleMouseLeave=e=>{const target=e.target;if(target.matches('button, a, .clickable, input, textarea')){setIsHovering(false);}};const handleMouseDown=()=>{setIsClicking(true);};const handleMouseUp=()=>{setIsClicking(false);};document.addEventListener('mousemove',handleMouseMove);document.addEventListener('mouseenter',handleMouseEnter,true);document.addEventListener('mouseleave',handleMouseLeave,true);document.addEventListener('mousedown',handleMouseDown);document.addEventListener('mouseup',handleMouseUp);return()=>{document.removeEventListener('mousemove',handleMouseMove);document.removeEventListener('mouseenter',handleMouseEnter,true);document.removeEventListener('mouseleave',handleMouseLeave,true);document.removeEventListener('mousedown',handleMouseDown);document.removeEventListener('mouseup',handleMouseUp);};},[]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:`cursor-follower ${isHovering?'hovering':''} ${isClicking?'clicking':''}`,style:{left:mousePosition.x,top:mousePosition.y},children:/*#__PURE__*/_jsx(\"div\",{className:\"cursor-inner\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"cursor-trail\",style:{left:mousePosition.x,top:mousePosition.y}})]});};export default CursorFollower;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Cursor<PERSON><PERSON>ow<PERSON>", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "isClicking", "setIsClicking", "handleMouseMove", "e", "clientX", "clientY", "handleMouseEnter", "target", "matches", "handleMouseLeave", "handleMouseDown", "handleMouseUp", "document", "addEventListener", "removeEventListener", "children", "className", "style", "left", "top"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/CursorFollower.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './CursorFollower.css';\n\nconst CursorFollower: React.FC = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const [isClicking, setIsClicking] = useState(false);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseEnter = (e: MouseEvent) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('button, a, .clickable, input, textarea')) {\n        setIsHovering(true);\n      }\n    };\n\n    const handleMouseLeave = (e: MouseEvent) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('button, a, .clickable, input, textarea')) {\n        setIsHovering(false);\n      }\n    };\n\n    const handleMouseDown = () => {\n      setIsClicking(true);\n    };\n\n    const handleMouseUp = () => {\n      setIsClicking(false);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseenter', handleMouseEnter, true);\n    document.addEventListener('mouseleave', handleMouseLeave, true);\n    document.addEventListener('mousedown', handleMouseDown);\n    document.addEventListener('mouseup', handleMouseUp);\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseenter', handleMouseEnter, true);\n      document.removeEventListener('mouseleave', handleMouseLeave, true);\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Inline styles required for dynamic mouse position tracking */}\n      <div\n        className={`cursor-follower ${isHovering ? 'hovering' : ''} ${isClicking ? 'clicking' : ''}`}\n        style={{\n          left: mousePosition.x,\n          top: mousePosition.y,\n        }}\n      >\n        <div className=\"cursor-inner\"></div>\n      </div>\n      {/* Inline styles required for dynamic mouse position tracking */}\n      <div\n        className=\"cursor-trail\"\n        style={{\n          left: mousePosition.x,\n          top: mousePosition.y,\n        }}\n      ></div>\n    </>\n  );\n};\n\nexport default CursorFollower;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGV,QAAQ,CAAC,CAAEW,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAClE,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgB,eAAe,CAAIC,CAAa,EAAK,CACzCR,gBAAgB,CAAC,CAAEC,CAAC,CAAEO,CAAC,CAACC,OAAO,CAAEP,CAAC,CAAEM,CAAC,CAACE,OAAQ,CAAC,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIH,CAAa,EAAK,CAC1C,KAAM,CAAAI,MAAM,CAAGJ,CAAC,CAACI,MAAqB,CACtC,GAAIA,MAAM,CAACC,OAAO,CAAC,wCAAwC,CAAC,CAAE,CAC5DT,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAU,gBAAgB,CAAIN,CAAa,EAAK,CAC1C,KAAM,CAAAI,MAAM,CAAGJ,CAAC,CAACI,MAAqB,CACtC,GAAIA,MAAM,CAACC,OAAO,CAAC,wCAAwC,CAAC,CAAE,CAC5DT,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAW,eAAe,CAAGA,CAAA,GAAM,CAC5BT,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAAU,aAAa,CAAGA,CAAA,GAAM,CAC1BV,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAEDW,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEX,eAAe,CAAC,CACvDU,QAAQ,CAACC,gBAAgB,CAAC,YAAY,CAAEP,gBAAgB,CAAE,IAAI,CAAC,CAC/DM,QAAQ,CAACC,gBAAgB,CAAC,YAAY,CAAEJ,gBAAgB,CAAE,IAAI,CAAC,CAC/DG,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEH,eAAe,CAAC,CACvDE,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEF,aAAa,CAAC,CAEnD,MAAO,IAAM,CACXC,QAAQ,CAACE,mBAAmB,CAAC,WAAW,CAAEZ,eAAe,CAAC,CAC1DU,QAAQ,CAACE,mBAAmB,CAAC,YAAY,CAAER,gBAAgB,CAAE,IAAI,CAAC,CAClEM,QAAQ,CAACE,mBAAmB,CAAC,YAAY,CAAEL,gBAAgB,CAAE,IAAI,CAAC,CAClEG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,CAAEJ,eAAe,CAAC,CAC1DE,QAAQ,CAACE,mBAAmB,CAAC,SAAS,CAAEH,aAAa,CAAC,CACxD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEnB,KAAA,CAAAF,SAAA,EAAAyB,QAAA,eAEE3B,IAAA,QACE4B,SAAS,CAAE,mBAAmBlB,UAAU,CAAG,UAAU,CAAG,EAAE,IAAIE,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CAC7FiB,KAAK,CAAE,CACLC,IAAI,CAAExB,aAAa,CAACE,CAAC,CACrBuB,GAAG,CAAEzB,aAAa,CAACG,CACrB,CAAE,CAAAkB,QAAA,cAEF3B,IAAA,QAAK4B,SAAS,CAAC,cAAc,CAAM,CAAC,CACjC,CAAC,cAEN5B,IAAA,QACE4B,SAAS,CAAC,cAAc,CACxBC,KAAK,CAAE,CACLC,IAAI,CAAExB,aAAa,CAACE,CAAC,CACrBuB,GAAG,CAAEzB,aAAa,CAACG,CACrB,CAAE,CACE,CAAC,EACP,CAAC,CAEP,CAAC,CAED,cAAe,CAAAJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}