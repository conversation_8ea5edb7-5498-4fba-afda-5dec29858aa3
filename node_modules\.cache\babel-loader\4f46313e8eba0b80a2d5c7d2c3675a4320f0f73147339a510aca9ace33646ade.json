{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\Experience.tsx\";\nimport React from 'react';\nimport './Experience.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Experience = () => {\n  const experiences = portfolioConfig.experience || [];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"experience\",\n    className: \"experience\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Professional Experience\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"experience-timeline\",\n        children: experiences.map((exp, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `experience-item ${index % 2 === 0 ? 'left' : 'right'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"experience-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"experience-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"position\",\n                children: exp.position\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"company-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"company\",\n                  children: exp.company\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"location\",\n                  children: exp.location\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"duration-type\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"duration\",\n                  children: exp.duration\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `type type-${exp.type}`,\n                  children: exp.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"experience-description\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: exp.description.map((item, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: item\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"experience-technologies\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Technologies Used:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tech-tags\",\n                children: exp.technologies.map((tech, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tech-tag\",\n                  children: tech\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"experience-marker\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"marker-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)]\n        }, exp.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = Experience;\nexport default Experience;\nvar _c;\n$RefreshReg$(_c, \"Experience\");", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsxDEV", "_jsxDEV", "Experience", "experiences", "experience", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "exp", "index", "position", "company", "location", "duration", "type", "description", "item", "idx", "technologies", "tech", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Experience.tsx"], "sourcesContent": ["import React from 'react';\nimport './Experience.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface ExperienceItem {\n  id: number;\n  company: string;\n  position: string;\n  duration: string;\n  location: string;\n  description: string[];\n  technologies: string[];\n  type: 'full-time' | 'part-time' | 'contract' | 'internship';\n}\n\nconst Experience: React.FC = () => {\n  const experiences: ExperienceItem[] = portfolioConfig.experience || [];\n\n  return (\n    <section id=\"experience\" className=\"experience\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Professional Experience</h2>\n        <div className=\"experience-timeline\">\n          {experiences.map((exp, index) => (\n            <div key={exp.id} className={`experience-item ${index % 2 === 0 ? 'left' : 'right'}`}>\n              <div className=\"experience-content\">\n                <div className=\"experience-header\">\n                  <h3 className=\"position\">{exp.position}</h3>\n                  <div className=\"company-info\">\n                    <h4 className=\"company\">{exp.company}</h4>\n                    <span className=\"location\">{exp.location}</span>\n                  </div>\n                  <div className=\"duration-type\">\n                    <span className=\"duration\">{exp.duration}</span>\n                    <span className={`type type-${exp.type}`}>{exp.type}</span>\n                  </div>\n                </div>\n                \n                <div className=\"experience-description\">\n                  <ul>\n                    {exp.description.map((item, idx) => (\n                      <li key={idx}>{item}</li>\n                    ))}\n                  </ul>\n                </div>\n\n                <div className=\"experience-technologies\">\n                  <h5>Technologies Used:</h5>\n                  <div className=\"tech-tags\">\n                    {exp.technologies.map((tech, idx) => (\n                      <span key={idx} className=\"tech-tag\">{tech}</span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n              <div className=\"experience-marker\">\n                <div className=\"marker-dot\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AACzB,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa5D,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,MAAMC,WAA6B,GAAGJ,eAAe,CAACK,UAAU,IAAI,EAAE;EAEtE,oBACEH,OAAA;IAASI,EAAE,EAAC,YAAY;IAACC,SAAS,EAAC,YAAY;IAAAC,QAAA,eAC7CN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBN,OAAA;QAAIK,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DV,OAAA;QAAKK,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EACjCJ,WAAW,CAACS,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC1Bb,OAAA;UAAkBK,SAAS,EAAE,mBAAmBQ,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,EAAG;UAAAP,QAAA,gBACnFN,OAAA;YAAKK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCN,OAAA;cAAKK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCN,OAAA;gBAAIK,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEM,GAAG,CAACE;cAAQ;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CV,OAAA;gBAAKK,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BN,OAAA;kBAAIK,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEM,GAAG,CAACG;gBAAO;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CV,OAAA;kBAAMK,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAEM,GAAG,CAACI;gBAAQ;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNV,OAAA;gBAAKK,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BN,OAAA;kBAAMK,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAEM,GAAG,CAACK;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChDV,OAAA;kBAAMK,SAAS,EAAE,aAAaO,GAAG,CAACM,IAAI,EAAG;kBAAAZ,QAAA,EAAEM,GAAG,CAACM;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENV,OAAA;cAAKK,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCN,OAAA;gBAAAM,QAAA,EACGM,GAAG,CAACO,WAAW,CAACR,GAAG,CAAC,CAACS,IAAI,EAAEC,GAAG,kBAC7BrB,OAAA;kBAAAM,QAAA,EAAec;gBAAI,GAAVC,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENV,OAAA;cAAKK,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCN,OAAA;gBAAAM,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BV,OAAA;gBAAKK,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBM,GAAG,CAACU,YAAY,CAACX,GAAG,CAAC,CAACY,IAAI,EAAEF,GAAG,kBAC9BrB,OAAA;kBAAgBK,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAEiB;gBAAI,GAA/BF,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CAClD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNV,OAAA;YAAKK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCN,OAAA;cAAKK,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA,GAjCEE,GAAG,CAACR,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACc,EAAA,GAjDIvB,UAAoB;AAmD1B,eAAeA,UAAU;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}