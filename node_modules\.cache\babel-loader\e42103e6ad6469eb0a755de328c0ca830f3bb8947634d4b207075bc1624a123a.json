{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./AccessibilityHelper.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AccessibilityHelper=()=>{const[isOpen,setIsOpen]=useState(false);const[fontSize,setFontSize]=useState(16);const[highContrast,setHighContrast]=useState(false);const[reducedMotion,setReducedMotion]=useState(false);useEffect(()=>{// Apply font size\ndocument.documentElement.style.fontSize=`${fontSize}px`;},[fontSize]);useEffect(()=>{// Apply high contrast\nif(highContrast){document.documentElement.classList.add('high-contrast');}else{document.documentElement.classList.remove('high-contrast');}},[highContrast]);useEffect(()=>{// Apply reduced motion\nif(reducedMotion){document.documentElement.classList.add('reduced-motion');}else{document.documentElement.classList.remove('reduced-motion');}},[reducedMotion]);const increaseFontSize=()=>{setFontSize(prev=>Math.min(prev+2,24));};const decreaseFontSize=()=>{setFontSize(prev=>Math.max(prev-2,12));};const resetFontSize=()=>{setFontSize(16);};const toggleHighContrast=()=>{setHighContrast(prev=>!prev);};const toggleReducedMotion=()=>{setReducedMotion(prev=>!prev);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"button\",{className:\"accessibility-toggle\",onClick:()=>setIsOpen(!isOpen),\"aria-label\":\"Open accessibility options\",\"aria-expanded\":`${isOpen}`,type:\"button\",children:/*#__PURE__*/_jsx(\"svg\",{width:\"24\",height:\"24\",viewBox:\"0 0 24 24\",fill:\"none\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 5.5C14.8 5.5 14.6 5.4 14.5 5.3L13 3.8C12.6 3.4 12.1 3.2 11.5 3.2S10.4 3.4 10 3.8L8.5 5.3C8.4 5.4 8.2 5.5 8 5.5L2 7V9L8 7.5V10.5C8 11.1 8.4 11.6 9 11.8L11 12.5V19C11 19.6 11.4 20 12 20S13 19.6 13 19V12.5L15 11.8C15.6 11.6 16 11.1 16 10.5V7.5L21 9Z\",fill:\"currentColor\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:`accessibility-panel ${isOpen?'open':''}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"accessibility-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Accessibility Options\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:()=>setIsOpen(false),\"aria-label\":\"Close accessibility options\",type:\"button\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"accessibility-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"accessibility-group\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Font Size\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"font-controls\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:decreaseFontSize,\"aria-label\":\"Decrease font size\",disabled:fontSize<=12,type:\"button\",children:\"A-\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-size-display\",children:[fontSize,\"px\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:increaseFontSize,\"aria-label\":\"Increase font size\",disabled:fontSize>=24,type:\"button\",children:\"A+\"}),/*#__PURE__*/_jsx(\"button\",{onClick:resetFontSize,\"aria-label\":\"Reset font size\",type:\"button\",children:\"Reset\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"accessibility-group\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Visual Options\"}),/*#__PURE__*/_jsxs(\"label\",{className:\"accessibility-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:highContrast,onChange:toggleHighContrast,\"aria-describedby\":\"contrast-desc\"}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),\"High Contrast Mode\"]}),/*#__PURE__*/_jsx(\"p\",{id:\"contrast-desc\",className:\"option-description\",children:\"Increases contrast for better visibility\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"accessibility-group\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Motion Options\"}),/*#__PURE__*/_jsxs(\"label\",{className:\"accessibility-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:reducedMotion,onChange:toggleReducedMotion,\"aria-describedby\":\"motion-desc\"}),/*#__PURE__*/_jsx(\"span\",{className:\"checkmark\"}),\"Reduce Motion\"]}),/*#__PURE__*/_jsx(\"p\",{id:\"motion-desc\",className:\"option-description\",children:\"Reduces animations and transitions\"})]})]})]}),isOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"accessibility-overlay\",onClick:()=>setIsOpen(false),\"aria-hidden\":\"true\"})]});};export default AccessibilityHelper;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AccessibilityHelper", "isOpen", "setIsOpen", "fontSize", "setFontSize", "highContrast", "setHighContrast", "reducedMotion", "setReducedMotion", "document", "documentElement", "style", "classList", "add", "remove", "increaseFontSize", "prev", "Math", "min", "decreaseFontSize", "max", "resetFontSize", "toggleHighContrast", "toggleReducedMotion", "children", "className", "onClick", "type", "width", "height", "viewBox", "fill", "d", "disabled", "checked", "onChange", "id"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/AccessibilityHelper.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './AccessibilityHelper.css';\n\nconst AccessibilityHelper: React.FC = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [fontSize, setFontSize] = useState(16);\n  const [highContrast, setHighContrast] = useState(false);\n  const [reducedMotion, setReducedMotion] = useState(false);\n\n  useEffect(() => {\n    // Apply font size\n    document.documentElement.style.fontSize = `${fontSize}px`;\n  }, [fontSize]);\n\n  useEffect(() => {\n    // Apply high contrast\n    if (highContrast) {\n      document.documentElement.classList.add('high-contrast');\n    } else {\n      document.documentElement.classList.remove('high-contrast');\n    }\n  }, [highContrast]);\n\n  useEffect(() => {\n    // Apply reduced motion\n    if (reducedMotion) {\n      document.documentElement.classList.add('reduced-motion');\n    } else {\n      document.documentElement.classList.remove('reduced-motion');\n    }\n  }, [reducedMotion]);\n\n  const increaseFontSize = () => {\n    setFontSize(prev => Math.min(prev + 2, 24));\n  };\n\n  const decreaseFontSize = () => {\n    setFontSize(prev => Math.max(prev - 2, 12));\n  };\n\n  const resetFontSize = () => {\n    setFontSize(16);\n  };\n\n  const toggleHighContrast = () => {\n    setHighContrast(prev => !prev);\n  };\n\n  const toggleReducedMotion = () => {\n    setReducedMotion(prev => !prev);\n  };\n\n  return (\n    <>\n      <button\n        className=\"accessibility-toggle\"\n        onClick={() => setIsOpen(!isOpen)}\n        aria-label=\"Open accessibility options\"\n        aria-expanded={`${isOpen}`}\n        type=\"button\"\n      >\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n          <path\n            d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 5.5C14.8 5.5 14.6 5.4 14.5 5.3L13 3.8C12.6 3.4 12.1 3.2 11.5 3.2S10.4 3.4 10 3.8L8.5 5.3C8.4 5.4 8.2 5.5 8 5.5L2 7V9L8 7.5V10.5C8 11.1 8.4 11.6 9 11.8L11 12.5V19C11 19.6 11.4 20 12 20S13 19.6 13 19V12.5L15 11.8C15.6 11.6 16 11.1 16 10.5V7.5L21 9Z\"\n            fill=\"currentColor\"\n          />\n        </svg>\n      </button>\n\n      <div className={`accessibility-panel ${isOpen ? 'open' : ''}`}>\n        <div className=\"accessibility-header\">\n          <h3>Accessibility Options</h3>\n          <button\n            className=\"close-btn\"\n            onClick={() => setIsOpen(false)}\n            aria-label=\"Close accessibility options\"\n            type=\"button\"\n          >\n            ×\n          </button>\n        </div>\n\n        <div className=\"accessibility-content\">\n          <div className=\"accessibility-group\">\n            <h4>Font Size</h4>\n            <div className=\"font-controls\">\n              <button\n                onClick={decreaseFontSize}\n                aria-label=\"Decrease font size\"\n                disabled={fontSize <= 12}\n                type=\"button\"\n              >\n                A-\n              </button>\n              <span className=\"font-size-display\">{fontSize}px</span>\n              <button\n                onClick={increaseFontSize}\n                aria-label=\"Increase font size\"\n                disabled={fontSize >= 24}\n                type=\"button\"\n              >\n                A+\n              </button>\n              <button\n                onClick={resetFontSize}\n                aria-label=\"Reset font size\"\n                type=\"button\"\n              >\n                Reset\n              </button>\n            </div>\n          </div>\n\n          <div className=\"accessibility-group\">\n            <h4>Visual Options</h4>\n            <label className=\"accessibility-checkbox\">\n              <input\n                type=\"checkbox\"\n                checked={highContrast}\n                onChange={toggleHighContrast}\n                aria-describedby=\"contrast-desc\"\n              />\n              <span className=\"checkmark\"></span>\n              High Contrast Mode\n            </label>\n            <p id=\"contrast-desc\" className=\"option-description\">\n              Increases contrast for better visibility\n            </p>\n          </div>\n\n          <div className=\"accessibility-group\">\n            <h4>Motion Options</h4>\n            <label className=\"accessibility-checkbox\">\n              <input\n                type=\"checkbox\"\n                checked={reducedMotion}\n                onChange={toggleReducedMotion}\n                aria-describedby=\"motion-desc\"\n              />\n              <span className=\"checkmark\"></span>\n              Reduce Motion\n            </label>\n            <p id=\"motion-desc\" className=\"option-description\">\n              Reduces animations and transitions\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {isOpen && (\n        <div\n          className=\"accessibility-overlay\"\n          onClick={() => setIsOpen(false)}\n          aria-hidden=\"true\"\n        ></div>\n      )}\n    </>\n  );\n};\n\nexport default AccessibilityHelper;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnC,KAAM,CAAAC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGV,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACW,QAAQ,CAAEC,WAAW,CAAC,CAAGZ,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACa,YAAY,CAAEC,eAAe,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACe,aAAa,CAAEC,gBAAgB,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CAEzDC,SAAS,CAAC,IAAM,CACd;AACAgB,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACR,QAAQ,CAAG,GAAGA,QAAQ,IAAI,CAC3D,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEdV,SAAS,CAAC,IAAM,CACd;AACA,GAAIY,YAAY,CAAE,CAChBI,QAAQ,CAACC,eAAe,CAACE,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CACzD,CAAC,IAAM,CACLJ,QAAQ,CAACC,eAAe,CAACE,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC,CAC5D,CACF,CAAC,CAAE,CAACT,YAAY,CAAC,CAAC,CAElBZ,SAAS,CAAC,IAAM,CACd;AACA,GAAIc,aAAa,CAAE,CACjBE,QAAQ,CAACC,eAAe,CAACE,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC1D,CAAC,IAAM,CACLJ,QAAQ,CAACC,eAAe,CAACE,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC,CAC7D,CACF,CAAC,CAAE,CAACP,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAAQ,gBAAgB,CAAGA,CAAA,GAAM,CAC7BX,WAAW,CAACY,IAAI,EAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,CAAG,CAAC,CAAE,EAAE,CAAC,CAAC,CAC7C,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAGA,CAAA,GAAM,CAC7Bf,WAAW,CAACY,IAAI,EAAIC,IAAI,CAACG,GAAG,CAACJ,IAAI,CAAG,CAAC,CAAE,EAAE,CAAC,CAAC,CAC7C,CAAC,CAED,KAAM,CAAAK,aAAa,CAAGA,CAAA,GAAM,CAC1BjB,WAAW,CAAC,EAAE,CAAC,CACjB,CAAC,CAED,KAAM,CAAAkB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BhB,eAAe,CAACU,IAAI,EAAI,CAACA,IAAI,CAAC,CAChC,CAAC,CAED,KAAM,CAAAO,mBAAmB,CAAGA,CAAA,GAAM,CAChCf,gBAAgB,CAACQ,IAAI,EAAI,CAACA,IAAI,CAAC,CACjC,CAAC,CAED,mBACEnB,KAAA,CAAAE,SAAA,EAAAyB,QAAA,eACE7B,IAAA,WACE8B,SAAS,CAAC,sBAAsB,CAChCC,OAAO,CAAEA,CAAA,GAAMxB,SAAS,CAAC,CAACD,MAAM,CAAE,CAClC,aAAW,4BAA4B,CACvC,gBAAe,GAAGA,MAAM,EAAG,CAC3B0B,IAAI,CAAC,QAAQ,CAAAH,QAAA,cAEb7B,IAAA,QAAKiC,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAAAP,QAAA,cACzD7B,IAAA,SACEqC,CAAC,CAAC,qVAAqV,CACvVD,IAAI,CAAC,cAAc,CACpB,CAAC,CACC,CAAC,CACA,CAAC,cAETlC,KAAA,QAAK4B,SAAS,CAAE,uBAAuBxB,MAAM,CAAG,MAAM,CAAG,EAAE,EAAG,CAAAuB,QAAA,eAC5D3B,KAAA,QAAK4B,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC7B,IAAA,OAAA6B,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9B7B,IAAA,WACE8B,SAAS,CAAC,WAAW,CACrBC,OAAO,CAAEA,CAAA,GAAMxB,SAAS,CAAC,KAAK,CAAE,CAChC,aAAW,6BAA6B,CACxCyB,IAAI,CAAC,QAAQ,CAAAH,QAAA,CACd,MAED,CAAQ,CAAC,EACN,CAAC,cAEN3B,KAAA,QAAK4B,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC3B,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7B,IAAA,OAAA6B,QAAA,CAAI,WAAS,CAAI,CAAC,cAClB3B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B7B,IAAA,WACE+B,OAAO,CAAEP,gBAAiB,CAC1B,aAAW,oBAAoB,CAC/Bc,QAAQ,CAAE9B,QAAQ,EAAI,EAAG,CACzBwB,IAAI,CAAC,QAAQ,CAAAH,QAAA,CACd,IAED,CAAQ,CAAC,cACT3B,KAAA,SAAM4B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,EAAErB,QAAQ,CAAC,IAAE,EAAM,CAAC,cACvDR,IAAA,WACE+B,OAAO,CAAEX,gBAAiB,CAC1B,aAAW,oBAAoB,CAC/BkB,QAAQ,CAAE9B,QAAQ,EAAI,EAAG,CACzBwB,IAAI,CAAC,QAAQ,CAAAH,QAAA,CACd,IAED,CAAQ,CAAC,cACT7B,IAAA,WACE+B,OAAO,CAAEL,aAAc,CACvB,aAAW,iBAAiB,CAC5BM,IAAI,CAAC,QAAQ,CAAAH,QAAA,CACd,OAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN3B,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7B,IAAA,OAAA6B,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB3B,KAAA,UAAO4B,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACvC7B,IAAA,UACEgC,IAAI,CAAC,UAAU,CACfO,OAAO,CAAE7B,YAAa,CACtB8B,QAAQ,CAAEb,kBAAmB,CAC7B,mBAAiB,eAAe,CACjC,CAAC,cACF3B,IAAA,SAAM8B,SAAS,CAAC,WAAW,CAAO,CAAC,qBAErC,EAAO,CAAC,cACR9B,IAAA,MAAGyC,EAAE,CAAC,eAAe,CAACX,SAAS,CAAC,oBAAoB,CAAAD,QAAA,CAAC,0CAErD,CAAG,CAAC,EACD,CAAC,cAEN3B,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7B,IAAA,OAAA6B,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB3B,KAAA,UAAO4B,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACvC7B,IAAA,UACEgC,IAAI,CAAC,UAAU,CACfO,OAAO,CAAE3B,aAAc,CACvB4B,QAAQ,CAAEZ,mBAAoB,CAC9B,mBAAiB,aAAa,CAC/B,CAAC,cACF5B,IAAA,SAAM8B,SAAS,CAAC,WAAW,CAAO,CAAC,gBAErC,EAAO,CAAC,cACR9B,IAAA,MAAGyC,EAAE,CAAC,aAAa,CAACX,SAAS,CAAC,oBAAoB,CAAAD,QAAA,CAAC,oCAEnD,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CAELvB,MAAM,eACLN,IAAA,QACE8B,SAAS,CAAC,uBAAuB,CACjCC,OAAO,CAAEA,CAAA,GAAMxB,SAAS,CAAC,KAAK,CAAE,CAChC,cAAY,MAAM,CACd,CACP,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}