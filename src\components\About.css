.about {
  padding: 5rem 0;
  background: #ffffff;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text {
  animation: fadeInLeft 1s ease-out;
}

.about-text p {
  font-size: 1.1rem;
  color: #4a5568;
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.stat:hover {
  transform: translateY(-5px);
}

.stat h3 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat p {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 600;
  margin: 0;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.image-placeholder {
  width: 350px;
  height: 400px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 3px dashed #cbd5e0;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea10 0%, #764ba210 100%);
}

.image-placeholder span {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.image-placeholder p {
  color: #718096;
  font-weight: 600;
  margin: 0;
}

/* Animations */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .about {
    padding: 3rem 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat {
    padding: 1rem;
  }

  .stat h3 {
    font-size: 2rem;
  }

  .image-placeholder {
    width: 280px;
    height: 320px;
  }

  .image-placeholder span {
    font-size: 3rem;
  }
}
