.about {
  padding: var(--space-24) 0;
  background: var(--bg-secondary);
  position: relative;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(6, 255, 165, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.about-text {
  opacity: 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;
}

.about-text p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.7;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.stat {
  text-align: center;
  padding: var(--space-6);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-400);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.stat:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl), var(--glow-primary);
  border-color: var(--primary);
}

.stat:hover::before {
  opacity: 0.05;
}

.stat h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-2);
  font-family: 'Space Grotesk', sans-serif;
}

.stat p {
  font-size: var(--text-sm);
  color: var(--text-muted);
  font-weight: 500;
  margin: 0;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.image-placeholder {
  width: 320px;
  height: 380px;
  background: var(--bg-secondary);
  border-radius: var(--radius-2xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--gray-300);
  transition: all var(--duration-normal) var(--ease-out);
}

.image-placeholder:hover {
  border-color: var(--primary);
  background: var(--gray-100);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.image-placeholder span {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
}

.image-placeholder p {
  color: var(--text-muted);
  font-weight: 500;
  margin: 0;
  font-size: var(--text-sm);
}

/* Clean Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .about {
    padding: var(--space-20) 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .about-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .stat {
    padding: var(--space-4);
  }

  .stat h3 {
    font-size: var(--text-2xl);
  }

  .image-placeholder {
    width: 280px;
    height: 320px;
  }

  .image-placeholder span {
    font-size: var(--text-5xl);
  }
}

@media (max-width: 480px) {
  .about-stats {
    grid-template-columns: 1fr;
  }

  .image-placeholder {
    width: 240px;
    height: 280px;
  }
}
