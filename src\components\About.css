.about {
  padding: var(--space-24) 0;
  background: var(--bg-secondary);
  position: relative;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 75% 25%, rgba(139, 92, 246, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 50% 10%, rgba(16, 185, 129, 0.04) 0%, transparent 60%);
  pointer-events: none;
}

.about::after {
  content: '';
  position: absolute;
  top: 30%;
  right: 15%;
  width: 200px;
  height: 200px;
  background: var(--gradient-secondary);
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.1;
  animation: float 15s ease-in-out infinite;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.about-text {
  opacity: 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;
}

.about-text p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.7;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.stat {
  text-align: center;
  padding: var(--space-8);
  background: var(--bg-elevated);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(99, 102, 241, 0.2);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-aurora);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.stat::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transform: translateX(-100%) translateY(-100%) rotate(45deg);
  transition: transform 0.6s ease;
}

.stat:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl), var(--glow-primary);
  border-color: var(--primary-light);
}

.stat:hover::before {
  opacity: 0.08;
}

.stat:hover::after {
  transform: translateX(100%) translateY(100%) rotate(45deg);
}

.stat h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-2);
  font-family: 'Space Grotesk', sans-serif;
}

.stat p {
  font-size: var(--text-sm);
  color: var(--text-muted);
  font-weight: 500;
  margin: 0;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.image-placeholder {
  width: 320px;
  height: 380px;
  background: var(--bg-secondary);
  border-radius: var(--radius-2xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--gray-300);
  transition: all var(--duration-normal) var(--ease-out);
}

.image-placeholder:hover {
  border-color: var(--primary);
  background: var(--gray-100);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.image-placeholder span {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
}

.image-placeholder p {
  color: var(--text-muted);
  font-weight: 500;
  margin: 0;
  font-size: var(--text-sm);
}

/* Clean Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .about {
    padding: var(--space-20) 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .about-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .stat {
    padding: var(--space-4);
  }

  .stat h3 {
    font-size: var(--text-2xl);
  }

  .image-placeholder {
    width: 280px;
    height: 320px;
  }

  .image-placeholder span {
    font-size: var(--text-5xl);
  }
}

@media (max-width: 480px) {
  .about-stats {
    grid-template-columns: 1fr;
  }

  .image-placeholder {
    width: 240px;
    height: 280px;
  }
}
