/* ABOUT SECTION - MINIMALIST PLATINUM ZEN THEME */
.about {
  padding: var(--space-24) 0;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
  position: relative;
  border-top: 1px solid var(--platinum);
  border-bottom: 1px solid var(--platinum);
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 80% 20%, rgba(229, 228, 226, 0.3) 0%, transparent 60%),
    radial-gradient(circle at 20% 80%, rgba(192, 191, 189, 0.2) 0%, transparent 60%);
  pointer-events: none;
}

.about::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(229, 228, 226, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 20s ease-in-out infinite;
}

/* Minimalist Section Title for About */
.about .section-title {
  color: #2d3748;
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 300;
  letter-spacing: 2px;
}

.about .section-title::after {
  background: linear-gradient(90deg, var(--platinum-dark) 0%, var(--platinum) 100%);
  height: 1px;
  width: 100px;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.about-text {
  opacity: 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;
}

.about-text p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.7;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.stat {
  text-align: center;
  padding: var(--space-8);
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-lg);
  border: 1px solid var(--platinum-dark);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-aurora);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.stat::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transform: translateX(-100%) translateY(-100%) rotate(45deg);
  transition: transform 0.6s ease;
}

.stat:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl), var(--glow-primary);
  border-color: var(--primary-light);
}

.stat:hover::before {
  opacity: 0.08;
}

.stat:hover::after {
  transform: translateX(100%) translateY(100%) rotate(45deg);
}

.stat h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-2);
  font-family: 'Space Grotesk', sans-serif;
}

.stat p {
  font-size: var(--text-sm);
  color: var(--text-muted);
  font-weight: 500;
  margin: 0;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.image-placeholder {
  width: 320px;
  height: 380px;
  background: var(--bg-secondary);
  border-radius: var(--radius-2xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--gray-300);
  transition: all var(--duration-normal) var(--ease-out);
}

.image-placeholder:hover {
  border-color: var(--primary);
  background: var(--gray-100);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.image-placeholder span {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
}

.image-placeholder p {
  color: var(--text-muted);
  font-weight: 500;
  margin: 0;
  font-size: var(--text-sm);
}

/* Clean Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .about {
    padding: var(--space-20) 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .about-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .stat {
    padding: var(--space-4);
  }

  .stat h3 {
    font-size: var(--text-2xl);
  }

  .image-placeholder {
    width: 280px;
    height: 320px;
  }

  .image-placeholder span {
    font-size: var(--text-5xl);
  }
}

@media (max-width: 480px) {
  .about-stats {
    grid-template-columns: 1fr;
  }

  .image-placeholder {
    width: 240px;
    height: 280px;
  }
}
