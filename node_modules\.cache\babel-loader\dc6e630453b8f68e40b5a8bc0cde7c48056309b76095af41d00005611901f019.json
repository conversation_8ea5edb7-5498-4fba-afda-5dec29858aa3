{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\DarkModeToggle.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './DarkModeToggle.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DarkModeToggle = () => {\n  _s();\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    if (savedTheme === 'dark' || !savedTheme && prefersDark) {\n      setIsDarkMode(true);\n      document.documentElement.classList.add('dark-mode');\n    }\n  }, []);\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = e => {\n      if (!localStorage.getItem('theme')) {\n        setIsDarkMode(e.matches);\n        if (e.matches) {\n          document.documentElement.classList.add('dark-mode');\n        } else {\n          document.documentElement.classList.remove('dark-mode');\n        }\n      }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n  const toggleDarkMode = () => {\n    const newDarkMode = !isDarkMode;\n    setIsDarkMode(newDarkMode);\n    if (newDarkMode) {\n      document.documentElement.classList.add('dark-mode');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark-mode');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `dark-mode-toggle ${isDarkMode ? 'dark' : 'light'}`,\n    onClick: toggleDarkMode,\n    \"aria-label\": `Switch to ${isDarkMode ? 'light' : 'dark'} mode`,\n    type: \"button\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toggle-track\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toggle-thumb\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toggle-icon\",\n          children: isDarkMode ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\",\n              fill: \"currentColor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"12\",\n              r: \"5\",\n              fill: \"currentColor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"toggle-label\",\n      children: isDarkMode ? 'Dark' : 'Light'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(DarkModeToggle, \"jZSDCHM8qUYa7sOOCe+CR2toAGQ=\");\n_c = DarkModeToggle;\nexport default DarkModeToggle;\nvar _c;\n$RefreshReg$(_c, \"DarkModeToggle\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "DarkModeToggle", "_s", "isDarkMode", "setIsDarkMode", "savedTheme", "localStorage", "getItem", "prefersDark", "window", "matchMedia", "matches", "document", "documentElement", "classList", "add", "mediaQuery", "handleChange", "e", "remove", "addEventListener", "removeEventListener", "toggleDarkMode", "newDarkMode", "setItem", "className", "onClick", "type", "children", "width", "height", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "stroke", "strokeWidth", "strokeLinecap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/DarkModeToggle.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './DarkModeToggle.css';\n\nconst DarkModeToggle: React.FC = () => {\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDarkMode(true);\n      document.documentElement.classList.add('dark-mode');\n    }\n  }, []);\n\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = (e: MediaQueryListEvent) => {\n      if (!localStorage.getItem('theme')) {\n        setIsDarkMode(e.matches);\n        if (e.matches) {\n          document.documentElement.classList.add('dark-mode');\n        } else {\n          document.documentElement.classList.remove('dark-mode');\n        }\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  const toggleDarkMode = () => {\n    const newDarkMode = !isDarkMode;\n    setIsDarkMode(newDarkMode);\n    \n    if (newDarkMode) {\n      document.documentElement.classList.add('dark-mode');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark-mode');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  return (\n    <button\n      className={`dark-mode-toggle ${isDarkMode ? 'dark' : 'light'}`}\n      onClick={toggleDarkMode}\n      aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}\n      type=\"button\"\n    >\n      <div className=\"toggle-track\">\n        <div className=\"toggle-thumb\">\n          <div className=\"toggle-icon\">\n            {isDarkMode ? (\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path\n                  d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n                  fill=\"currentColor\"\n                />\n              </svg>\n            ) : (\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"5\" fill=\"currentColor\" />\n                <path\n                  d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                />\n              </svg>\n            )}\n          </div>\n        </div>\n      </div>\n      <span className=\"toggle-label\">\n        {isDarkMode ? 'Dark' : 'Light'}\n      </span>\n    </button>\n  );\n};\n\nexport default DarkModeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;IAE7E,IAAIN,UAAU,KAAK,MAAM,IAAK,CAACA,UAAU,IAAIG,WAAY,EAAE;MACzDJ,aAAa,CAAC,IAAI,CAAC;MACnBQ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;IACrD;EACF,CAAC,EAAE,EAAE,CAAC;EAENjB,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,UAAU,GAAGP,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IACpE,MAAMO,YAAY,GAAIC,CAAsB,IAAK;MAC/C,IAAI,CAACZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClCH,aAAa,CAACc,CAAC,CAACP,OAAO,CAAC;QACxB,IAAIO,CAAC,CAACP,OAAO,EAAE;UACbC,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACK,MAAM,CAAC,WAAW,CAAC;QACxD;MACF;IACF,CAAC;IAEDH,UAAU,CAACI,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACnD,OAAO,MAAMD,UAAU,CAACK,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,WAAW,GAAG,CAACpB,UAAU;IAC/BC,aAAa,CAACmB,WAAW,CAAC;IAE1B,IAAIA,WAAW,EAAE;MACfX,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;MACnDT,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IACvC,CAAC,MAAM;MACLZ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACK,MAAM,CAAC,WAAW,CAAC;MACtDb,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;IACxC;EACF,CAAC;EAED,oBACExB,OAAA;IACEyB,SAAS,EAAE,oBAAoBtB,UAAU,GAAG,MAAM,GAAG,OAAO,EAAG;IAC/DuB,OAAO,EAAEJ,cAAe;IACxB,cAAY,aAAanB,UAAU,GAAG,OAAO,GAAG,MAAM,OAAQ;IAC9DwB,IAAI,EAAC,QAAQ;IAAAC,QAAA,gBAEb5B,OAAA;MAAKyB,SAAS,EAAC,cAAc;MAAAG,QAAA,eAC3B5B,OAAA;QAAKyB,SAAS,EAAC,cAAc;QAAAG,QAAA,eAC3B5B,OAAA;UAAKyB,SAAS,EAAC,aAAa;UAAAG,QAAA,EACzBzB,UAAU,gBACTH,OAAA;YAAK6B,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAJ,QAAA,eACzD5B,OAAA;cACEiC,CAAC,EAAC,iDAAiD;cACnDD,IAAI,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENrC,OAAA;YAAK6B,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAJ,QAAA,gBACzD5B,OAAA;cAAQsC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC,GAAG;cAACR,IAAI,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDrC,OAAA;cACEiC,CAAC,EAAC,oHAAoH;cACtHQ,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,GAAG;cACfC,aAAa,EAAC;YAAO;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNrC,OAAA;MAAMyB,SAAS,EAAC,cAAc;MAAAG,QAAA,EAC3BzB,UAAU,GAAG,MAAM,GAAG;IAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAACnC,EAAA,CAjFID,cAAwB;AAAA2C,EAAA,GAAxB3C,cAAwB;AAmF9B,eAAeA,cAAc;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}