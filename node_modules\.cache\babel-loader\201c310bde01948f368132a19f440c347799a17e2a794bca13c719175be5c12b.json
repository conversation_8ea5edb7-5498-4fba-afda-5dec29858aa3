{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./DarkModeToggle.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DarkModeToggle=()=>{const[isDarkMode,setIsDarkMode]=useState(false);useEffect(()=>{// Check for saved theme preference or default to light mode\nconst savedTheme=localStorage.getItem('theme');const prefersDark=window.matchMedia('(prefers-color-scheme: dark)').matches;if(savedTheme==='dark'||!savedTheme&&prefersDark){setIsDarkMode(true);document.documentElement.classList.add('dark-mode');}},[]);useEffect(()=>{// Listen for system theme changes\nconst mediaQuery=window.matchMedia('(prefers-color-scheme: dark)');const handleChange=e=>{if(!localStorage.getItem('theme')){setIsDarkMode(e.matches);if(e.matches){document.documentElement.classList.add('dark-mode');}else{document.documentElement.classList.remove('dark-mode');}}};mediaQuery.addEventListener('change',handleChange);return()=>mediaQuery.removeEventListener('change',handleChange);},[]);const toggleDarkMode=()=>{const newDarkMode=!isDarkMode;setIsDarkMode(newDarkMode);if(newDarkMode){document.documentElement.classList.add('dark-mode');localStorage.setItem('theme','dark');}else{document.documentElement.classList.remove('dark-mode');localStorage.setItem('theme','light');}};return/*#__PURE__*/_jsxs(\"button\",{className:`dark-mode-toggle ${isDarkMode?'dark':'light'}`,onClick:toggleDarkMode,\"aria-label\":`Switch to ${isDarkMode?'light':'dark'} mode`,type:\"button\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"toggle-track\",children:/*#__PURE__*/_jsx(\"div\",{className:\"toggle-thumb\",children:/*#__PURE__*/_jsx(\"div\",{className:\"toggle-icon\",children:isDarkMode?/*#__PURE__*/_jsx(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\",fill:\"currentColor\"})}):/*#__PURE__*/_jsxs(\"svg\",{width:\"16\",height:\"16\",viewBox:\"0 0 24 24\",fill:\"none\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"12\",r:\"5\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\"})]})})})}),/*#__PURE__*/_jsx(\"span\",{className:\"toggle-label\",children:isDarkMode?'Dark':'Light'})]});};export default DarkModeToggle;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "DarkModeToggle", "isDarkMode", "setIsDarkMode", "savedTheme", "localStorage", "getItem", "prefersDark", "window", "matchMedia", "matches", "document", "documentElement", "classList", "add", "mediaQuery", "handleChange", "e", "remove", "addEventListener", "removeEventListener", "toggleDarkMode", "newDarkMode", "setItem", "className", "onClick", "type", "children", "width", "height", "viewBox", "fill", "d", "cx", "cy", "r", "stroke", "strokeWidth", "strokeLinecap"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/DarkModeToggle.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './DarkModeToggle.css';\n\nconst DarkModeToggle: React.FC = () => {\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDarkMode(true);\n      document.documentElement.classList.add('dark-mode');\n    }\n  }, []);\n\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = (e: MediaQueryListEvent) => {\n      if (!localStorage.getItem('theme')) {\n        setIsDarkMode(e.matches);\n        if (e.matches) {\n          document.documentElement.classList.add('dark-mode');\n        } else {\n          document.documentElement.classList.remove('dark-mode');\n        }\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  const toggleDarkMode = () => {\n    const newDarkMode = !isDarkMode;\n    setIsDarkMode(newDarkMode);\n    \n    if (newDarkMode) {\n      document.documentElement.classList.add('dark-mode');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark-mode');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  return (\n    <button\n      className={`dark-mode-toggle ${isDarkMode ? 'dark' : 'light'}`}\n      onClick={toggleDarkMode}\n      aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}\n      type=\"button\"\n    >\n      <div className=\"toggle-track\">\n        <div className=\"toggle-thumb\">\n          <div className=\"toggle-icon\">\n            {isDarkMode ? (\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path\n                  d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n                  fill=\"currentColor\"\n                />\n              </svg>\n            ) : (\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"5\" fill=\"currentColor\" />\n                <path\n                  d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                />\n              </svg>\n            )}\n          </div>\n        </div>\n      </div>\n      <span className=\"toggle-label\">\n        {isDarkMode ? 'Dark' : 'Light'}\n      </span>\n    </button>\n  );\n};\n\nexport default DarkModeToggle;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGR,QAAQ,CAAC,KAAK,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAQ,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAChD,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,CAE7E,GAAIN,UAAU,GAAK,MAAM,EAAK,CAACA,UAAU,EAAIG,WAAY,CAAE,CACzDJ,aAAa,CAAC,IAAI,CAAC,CACnBQ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC,CACrD,CACF,CAAC,CAAE,EAAE,CAAC,CAENlB,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAmB,UAAU,CAAGP,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CACpE,KAAM,CAAAO,YAAY,CAAIC,CAAsB,EAAK,CAC/C,GAAI,CAACZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAE,CAClCH,aAAa,CAACc,CAAC,CAACP,OAAO,CAAC,CACxB,GAAIO,CAAC,CAACP,OAAO,CAAE,CACbC,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC,CACrD,CAAC,IAAM,CACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACK,MAAM,CAAC,WAAW,CAAC,CACxD,CACF,CACF,CAAC,CAEDH,UAAU,CAACI,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CACnD,MAAO,IAAMD,UAAU,CAACK,mBAAmB,CAAC,QAAQ,CAAEJ,YAAY,CAAC,CACrE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAK,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,WAAW,CAAG,CAACpB,UAAU,CAC/BC,aAAa,CAACmB,WAAW,CAAC,CAE1B,GAAIA,WAAW,CAAE,CACfX,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC,CACnDT,YAAY,CAACkB,OAAO,CAAC,OAAO,CAAE,MAAM,CAAC,CACvC,CAAC,IAAM,CACLZ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACK,MAAM,CAAC,WAAW,CAAC,CACtDb,YAAY,CAACkB,OAAO,CAAC,OAAO,CAAE,OAAO,CAAC,CACxC,CACF,CAAC,CAED,mBACEvB,KAAA,WACEwB,SAAS,CAAE,oBAAoBtB,UAAU,CAAG,MAAM,CAAG,OAAO,EAAG,CAC/DuB,OAAO,CAAEJ,cAAe,CACxB,aAAY,aAAanB,UAAU,CAAG,OAAO,CAAG,MAAM,OAAQ,CAC9DwB,IAAI,CAAC,QAAQ,CAAAC,QAAA,eAEb7B,IAAA,QAAK0B,SAAS,CAAC,cAAc,CAAAG,QAAA,cAC3B7B,IAAA,QAAK0B,SAAS,CAAC,cAAc,CAAAG,QAAA,cAC3B7B,IAAA,QAAK0B,SAAS,CAAC,aAAa,CAAAG,QAAA,CACzBzB,UAAU,cACTJ,IAAA,QAAK8B,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAAAJ,QAAA,cACzD7B,IAAA,SACEkC,CAAC,CAAC,iDAAiD,CACnDD,IAAI,CAAC,cAAc,CACpB,CAAC,CACC,CAAC,cAEN/B,KAAA,QAAK4B,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAAAJ,QAAA,eACzD7B,IAAA,WAAQmC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACJ,IAAI,CAAC,cAAc,CAAE,CAAC,cACpDjC,IAAA,SACEkC,CAAC,CAAC,oHAAoH,CACtHI,MAAM,CAAC,cAAc,CACrBC,WAAW,CAAC,GAAG,CACfC,aAAa,CAAC,OAAO,CACtB,CAAC,EACC,CACN,CACE,CAAC,CACH,CAAC,CACH,CAAC,cACNxC,IAAA,SAAM0B,SAAS,CAAC,cAAc,CAAAG,QAAA,CAC3BzB,UAAU,CAAG,MAAM,CAAG,OAAO,CAC1B,CAAC,EACD,CAAC,CAEb,CAAC,CAED,cAAe,CAAAD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}