{"ast": null, "code": "import React,{useState}from'react';import'./Certificates.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Certificates=()=>{const[activeFilter,setActiveFilter]=useState('All');const certificates=portfolioConfig.certificates||[];const categories=['All',...Array.from(new Set(certificates.map(cert=>cert.category)))];const filteredCertificates=activeFilter==='All'?certificates:certificates.filter(cert=>cert.category===activeFilter);return/*#__PURE__*/_jsx(\"section\",{id:\"certificates\",className:\"certificates\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"Certifications & Achievements\"}),/*#__PURE__*/_jsx(\"div\",{className:\"certificate-filters\",children:categories.map(category=>/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:`filter-btn ${activeFilter===category?'active':''}`,onClick:()=>setActiveFilter(category),children:category},category))}),/*#__PURE__*/_jsx(\"div\",{className:\"certificates-grid\",children:filteredCertificates.map(cert=>/*#__PURE__*/_jsxs(\"div\",{className:\"certificate-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"certificate-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"certificate-image\",children:/*#__PURE__*/_jsx(\"span\",{className:\"cert-icon\",children:cert.image})}),/*#__PURE__*/_jsx(\"div\",{className:\"certificate-badge\",children:/*#__PURE__*/_jsx(\"span\",{className:\"category-badge\",children:cert.category})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"certificate-content\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"certificate-name\",children:cert.name}),/*#__PURE__*/_jsx(\"h4\",{className:\"certificate-issuer\",children:cert.issuer}),/*#__PURE__*/_jsxs(\"p\",{className:\"certificate-date\",children:[\"Issued: \",cert.date]}),/*#__PURE__*/_jsx(\"p\",{className:\"certificate-description\",children:cert.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"certificate-skills\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"Skills Covered:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"skills-tags\",children:cert.skills.map((skill,idx)=>/*#__PURE__*/_jsx(\"span\",{className:\"skill-tag\",children:skill},idx))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"certificate-actions\",children:[cert.credentialId&&/*#__PURE__*/_jsxs(\"div\",{className:\"credential-id\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"ID:\"}),\" \",cert.credentialId]}),cert.verificationUrl&&/*#__PURE__*/_jsx(\"a\",{href:cert.verificationUrl,className:\"verify-btn\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"Verify Certificate\"})]})]})]},cert.id))}),filteredCertificates.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"no-certificates\",children:/*#__PURE__*/_jsx(\"p\",{children:\"No certificates found for the selected category.\"})})]})});};export default Certificates;", "map": {"version": 3, "names": ["React", "useState", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Certificates", "activeFilter", "setActiveFilter", "certificates", "categories", "Array", "from", "Set", "map", "cert", "category", "filteredCertificates", "filter", "id", "className", "children", "type", "onClick", "image", "name", "issuer", "date", "description", "skills", "skill", "idx", "credentialId", "verificationUrl", "href", "target", "rel", "length"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Certificates.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Certificates.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface Certificate {\n  id: number;\n  name: string;\n  issuer: string;\n  date: string;\n  credentialId?: string;\n  verificationUrl?: string;\n  description: string;\n  skills: string[];\n  category: string;\n  image: string;\n}\n\nconst Certificates: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('All');\n  const certificates: Certificate[] = portfolioConfig.certificates || [];\n  \n  const categories = ['All', ...Array.from(new Set(certificates.map(cert => cert.category)))];\n  \n  const filteredCertificates = activeFilter === 'All' \n    ? certificates \n    : certificates.filter(cert => cert.category === activeFilter);\n\n  return (\n    <section id=\"certificates\" className=\"certificates\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Certifications & Achievements</h2>\n        \n        <div className=\"certificate-filters\">\n          {categories.map((category) => (\n            <button\n              key={category}\n              type=\"button\"\n              className={`filter-btn ${activeFilter === category ? 'active' : ''}`}\n              onClick={() => setActiveFilter(category)}\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"certificates-grid\">\n          {filteredCertificates.map((cert) => (\n            <div key={cert.id} className=\"certificate-card\">\n              <div className=\"certificate-header\">\n                <div className=\"certificate-image\">\n                  <span className=\"cert-icon\">{cert.image}</span>\n                </div>\n                <div className=\"certificate-badge\">\n                  <span className=\"category-badge\">{cert.category}</span>\n                </div>\n              </div>\n              \n              <div className=\"certificate-content\">\n                <h3 className=\"certificate-name\">{cert.name}</h3>\n                <h4 className=\"certificate-issuer\">{cert.issuer}</h4>\n                <p className=\"certificate-date\">Issued: {cert.date}</p>\n                \n                <p className=\"certificate-description\">{cert.description}</p>\n                \n                <div className=\"certificate-skills\">\n                  <h5>Skills Covered:</h5>\n                  <div className=\"skills-tags\">\n                    {cert.skills.map((skill, idx) => (\n                      <span key={idx} className=\"skill-tag\">{skill}</span>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"certificate-actions\">\n                  {cert.credentialId && (\n                    <div className=\"credential-id\">\n                      <strong>ID:</strong> {cert.credentialId}\n                    </div>\n                  )}\n                  {cert.verificationUrl && (\n                    <a \n                      href={cert.verificationUrl} \n                      className=\"verify-btn\"\n                      target=\"_blank\" \n                      rel=\"noopener noreferrer\"\n                    >\n                      Verify Certificate\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {filteredCertificates.length === 0 && (\n          <div className=\"no-certificates\">\n            <p>No certificates found for the selected category.</p>\n          </div>\n        )}\n      </div>\n    </section>\n  );\n};\n\nexport default Certificates;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,oBAAoB,CAC3B,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAe5D,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGR,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAAS,YAA2B,CAAGR,eAAe,CAACQ,YAAY,EAAI,EAAE,CAEtE,KAAM,CAAAC,UAAU,CAAG,CAAC,KAAK,CAAE,GAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAACJ,YAAY,CAACK,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE3F,KAAM,CAAAC,oBAAoB,CAAGV,YAAY,GAAK,KAAK,CAC/CE,YAAY,CACZA,YAAY,CAACS,MAAM,CAACH,IAAI,EAAIA,IAAI,CAACC,QAAQ,GAAKT,YAAY,CAAC,CAE/D,mBACEJ,IAAA,YAASgB,EAAE,CAAC,cAAc,CAACC,SAAS,CAAC,cAAc,CAAAC,QAAA,cACjDhB,KAAA,QAAKe,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlB,IAAA,OAAIiB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,+BAA6B,CAAI,CAAC,cAEhElB,IAAA,QAAKiB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCX,UAAU,CAACI,GAAG,CAAEE,QAAQ,eACvBb,IAAA,WAEEmB,IAAI,CAAC,QAAQ,CACbF,SAAS,CAAE,cAAcb,YAAY,GAAKS,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CACrEO,OAAO,CAAEA,CAAA,GAAMf,eAAe,CAACQ,QAAQ,CAAE,CAAAK,QAAA,CAExCL,QAAQ,EALJA,QAMC,CACT,CAAC,CACC,CAAC,cAENb,IAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC/BJ,oBAAoB,CAACH,GAAG,CAAEC,IAAI,eAC7BV,KAAA,QAAmBe,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7ChB,KAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjClB,IAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChClB,IAAA,SAAMiB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEN,IAAI,CAACS,KAAK,CAAO,CAAC,CAC5C,CAAC,cACNrB,IAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChClB,IAAA,SAAMiB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEN,IAAI,CAACC,QAAQ,CAAO,CAAC,CACpD,CAAC,EACH,CAAC,cAENX,KAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClClB,IAAA,OAAIiB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEN,IAAI,CAACU,IAAI,CAAK,CAAC,cACjDtB,IAAA,OAAIiB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEN,IAAI,CAACW,MAAM,CAAK,CAAC,cACrDrB,KAAA,MAAGe,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,UAAQ,CAACN,IAAI,CAACY,IAAI,EAAI,CAAC,cAEvDxB,IAAA,MAAGiB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEN,IAAI,CAACa,WAAW,CAAI,CAAC,cAE7DvB,KAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjClB,IAAA,OAAAkB,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBlB,IAAA,QAAKiB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBN,IAAI,CAACc,MAAM,CAACf,GAAG,CAAC,CAACgB,KAAK,CAAEC,GAAG,gBAC1B5B,IAAA,SAAgBiB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAES,KAAK,EAAjCC,GAAwC,CACpD,CAAC,CACC,CAAC,EACH,CAAC,cAEN1B,KAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EACjCN,IAAI,CAACiB,YAAY,eAChB3B,KAAA,QAAKe,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BlB,IAAA,WAAAkB,QAAA,CAAQ,KAAG,CAAQ,CAAC,IAAC,CAACN,IAAI,CAACiB,YAAY,EACpC,CACN,CACAjB,IAAI,CAACkB,eAAe,eACnB9B,IAAA,MACE+B,IAAI,CAAEnB,IAAI,CAACkB,eAAgB,CAC3Bb,SAAS,CAAC,YAAY,CACtBe,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CAAAf,QAAA,CAC1B,oBAED,CAAG,CACJ,EACE,CAAC,EACH,CAAC,GA3CEN,IAAI,CAACI,EA4CV,CACN,CAAC,CACC,CAAC,CAELF,oBAAoB,CAACoB,MAAM,GAAK,CAAC,eAChClC,IAAA,QAAKiB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BlB,IAAA,MAAAkB,QAAA,CAAG,kDAAgD,CAAG,CAAC,CACpD,CACN,EACE,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAf,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}