{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\Certificates.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Certificates.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Certificates = () => {\n  _s();\n  const [activeFilter, setActiveFilter] = useState('All');\n  const certificates = portfolioConfig.certificates || [];\n  const categories = ['All', ...Array.from(new Set(certificates.map(cert => cert.category)))];\n  const filteredCertificates = activeFilter === 'All' ? certificates : certificates.filter(cert => cert.category === activeFilter);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"certificates\",\n    className: \"certificates\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Certifications & Achievements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"certificate-filters\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: `filter-btn ${activeFilter === category ? 'active' : ''}`,\n          onClick: () => setActiveFilter(category),\n          children: category\n        }, category, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"certificates-grid\",\n        children: filteredCertificates.map(cert => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"certificate-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"certificate-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-image\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cert-icon\",\n                children: cert.image\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-badge\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category-badge\",\n                children: cert.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"certificate-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"certificate-name\",\n              children: cert.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"certificate-issuer\",\n              children: cert.issuer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"certificate-date\",\n              children: [\"Issued: \", cert.date]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"certificate-description\",\n              children: cert.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-skills\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Skills Covered:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-tags\",\n                children: cert.skills.map((skill, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-actions\",\n              children: [cert.credentialId && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"credential-id\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"ID:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 23\n                }, this), \" \", cert.credentialId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 21\n              }, this), cert.verificationUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                href: cert.verificationUrl,\n                className: \"verify-btn\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"Verify Certificate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)]\n        }, cert.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), filteredCertificates.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-certificates\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No certificates found for the selected category.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Certificates, \"ozHZ0P52e9OmiFFgr1T32bValso=\");\n_c = Certificates;\nexport default Certificates;\nvar _c;\n$RefreshReg$(_c, \"Certificates\");", "map": {"version": 3, "names": ["React", "useState", "portfolioConfig", "jsxDEV", "_jsxDEV", "Certificates", "_s", "activeFilter", "setActiveFilter", "certificates", "categories", "Array", "from", "Set", "map", "cert", "category", "filteredCertificates", "filter", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "image", "name", "issuer", "date", "description", "skills", "skill", "idx", "credentialId", "verificationUrl", "href", "target", "rel", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Certificates.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Certificates.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface Certificate {\n  id: number;\n  name: string;\n  issuer: string;\n  date: string;\n  credentialId?: string;\n  verificationUrl?: string;\n  description: string;\n  skills: string[];\n  category: string;\n  image: string;\n}\n\nconst Certificates: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('All');\n  const certificates: Certificate[] = portfolioConfig.certificates || [];\n  \n  const categories = ['All', ...Array.from(new Set(certificates.map(cert => cert.category)))];\n  \n  const filteredCertificates = activeFilter === 'All' \n    ? certificates \n    : certificates.filter(cert => cert.category === activeFilter);\n\n  return (\n    <section id=\"certificates\" className=\"certificates\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Certifications & Achievements</h2>\n        \n        <div className=\"certificate-filters\">\n          {categories.map((category) => (\n            <button\n              key={category}\n              type=\"button\"\n              className={`filter-btn ${activeFilter === category ? 'active' : ''}`}\n              onClick={() => setActiveFilter(category)}\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"certificates-grid\">\n          {filteredCertificates.map((cert) => (\n            <div key={cert.id} className=\"certificate-card\">\n              <div className=\"certificate-header\">\n                <div className=\"certificate-image\">\n                  <span className=\"cert-icon\">{cert.image}</span>\n                </div>\n                <div className=\"certificate-badge\">\n                  <span className=\"category-badge\">{cert.category}</span>\n                </div>\n              </div>\n              \n              <div className=\"certificate-content\">\n                <h3 className=\"certificate-name\">{cert.name}</h3>\n                <h4 className=\"certificate-issuer\">{cert.issuer}</h4>\n                <p className=\"certificate-date\">Issued: {cert.date}</p>\n                \n                <p className=\"certificate-description\">{cert.description}</p>\n                \n                <div className=\"certificate-skills\">\n                  <h5>Skills Covered:</h5>\n                  <div className=\"skills-tags\">\n                    {cert.skills.map((skill, idx) => (\n                      <span key={idx} className=\"skill-tag\">{skill}</span>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"certificate-actions\">\n                  {cert.credentialId && (\n                    <div className=\"credential-id\">\n                      <strong>ID:</strong> {cert.credentialId}\n                    </div>\n                  )}\n                  {cert.verificationUrl && (\n                    <a \n                      href={cert.verificationUrl} \n                      className=\"verify-btn\"\n                      target=\"_blank\" \n                      rel=\"noopener noreferrer\"\n                    >\n                      Verify Certificate\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {filteredCertificates.length === 0 && (\n          <div className=\"no-certificates\">\n            <p>No certificates found for the selected category.</p>\n          </div>\n        )}\n      </div>\n    </section>\n  );\n};\n\nexport default Certificates;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,oBAAoB;AAC3B,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe5D,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMQ,YAA2B,GAAGP,eAAe,CAACO,YAAY,IAAI,EAAE;EAEtE,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACJ,YAAY,CAACK,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE3F,MAAMC,oBAAoB,GAAGV,YAAY,KAAK,KAAK,GAC/CE,YAAY,GACZA,YAAY,CAACS,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKT,YAAY,CAAC;EAE/D,oBACEH,OAAA;IAASe,EAAE,EAAC,cAAc;IAACC,SAAS,EAAC,cAAc;IAAAC,QAAA,eACjDjB,OAAA;MAAKgB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjB,OAAA;QAAIgB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEhErB,OAAA;QAAKgB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EACjCX,UAAU,CAACI,GAAG,CAAEE,QAAQ,iBACvBZ,OAAA;UAEEsB,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAE,cAAcb,YAAY,KAAKS,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrEW,OAAO,EAAEA,CAAA,KAAMnB,eAAe,CAACQ,QAAQ,CAAE;UAAAK,QAAA,EAExCL;QAAQ,GALJA,QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrB,OAAA;QAAKgB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/BJ,oBAAoB,CAACH,GAAG,CAAEC,IAAI,iBAC7BX,OAAA;UAAmBgB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7CjB,OAAA;YAAKgB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCjB,OAAA;cAAKgB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCjB,OAAA;gBAAMgB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEN,IAAI,CAACa;cAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNrB,OAAA;cAAKgB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCjB,OAAA;gBAAMgB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEN,IAAI,CAACC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrB,OAAA;YAAKgB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCjB,OAAA;cAAIgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEN,IAAI,CAACc;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjDrB,OAAA;cAAIgB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEN,IAAI,CAACe;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDrB,OAAA;cAAGgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,UAAQ,EAACN,IAAI,CAACgB,IAAI;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEvDrB,OAAA;cAAGgB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEN,IAAI,CAACiB;YAAW;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE7DrB,OAAA;cAAKgB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCjB,OAAA;gBAAAiB,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBrB,OAAA;gBAAKgB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBN,IAAI,CAACkB,MAAM,CAACnB,GAAG,CAAC,CAACoB,KAAK,EAAEC,GAAG,kBAC1B/B,OAAA;kBAAgBgB,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEa;gBAAK,GAAjCC,GAAG;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrB,OAAA;cAAKgB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GACjCN,IAAI,CAACqB,YAAY,iBAChBhC,OAAA;gBAAKgB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjB,OAAA;kBAAAiB,QAAA,EAAQ;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACV,IAAI,CAACqB,YAAY;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACN,EACAV,IAAI,CAACsB,eAAe,iBACnBjC,OAAA;gBACEkC,IAAI,EAAEvB,IAAI,CAACsB,eAAgB;gBAC3BjB,SAAS,EAAC,YAAY;gBACtBmB,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBAAAnB,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA3CEV,IAAI,CAACI,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4CZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELR,oBAAoB,CAACwB,MAAM,KAAK,CAAC,iBAChCrC,OAAA;QAAKgB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BjB,OAAA;UAAAiB,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACnB,EAAA,CAtFID,YAAsB;AAAAqC,EAAA,GAAtBrC,YAAsB;AAwF5B,eAAeA,YAAY;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}