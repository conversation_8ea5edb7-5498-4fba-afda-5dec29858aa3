.certificates {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.certificate-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.certificates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.certificate-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: fadeInUp 1s ease-out;
  position: relative;
}

.certificate-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.certificate-header {
  position: relative;
  height: 150px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.certificate-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 20px 20px;
}

.certificate-image {
  z-index: 1;
  position: relative;
}

.cert-icon {
  font-size: 3rem;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.certificate-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 2;
}

.category-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.certificate-content {
  padding: 2rem;
}

.certificate-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.certificate-issuer {
  font-size: 1.1rem;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.certificate-date {
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 1rem;
  font-weight: 500;
}

.certificate-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.certificate-skills h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.skill-tag {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #e2e8f0;
}

.certificate-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.credential-id {
  font-size: 0.8rem;
  color: #718096;
}

.credential-id strong {
  color: #4a5568;
}

.verify-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.verify-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.no-certificates {
  text-align: center;
  padding: 3rem;
  color: #718096;
  font-size: 1.1rem;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for certificate cards */
.certificate-card:nth-child(1) {
  animation-delay: 0s;
}

.certificate-card:nth-child(2) {
  animation-delay: 0.2s;
}

.certificate-card:nth-child(3) {
  animation-delay: 0.4s;
}

.certificate-card:nth-child(4) {
  animation-delay: 0.6s;
}

.certificate-card:nth-child(5) {
  animation-delay: 0.8s;
}

.certificate-card:nth-child(6) {
  animation-delay: 1s;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .certificates {
    padding: 3rem 0;
  }

  .certificates-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .certificate-content {
    padding: 1.5rem;
  }

  .certificate-name {
    font-size: 1.2rem;
  }

  .certificate-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .verify-btn {
    text-align: center;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}
