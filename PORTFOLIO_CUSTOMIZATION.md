# Portfolio Customization Guide

This portfolio is designed to be easily customizable. Follow this guide to personalize it with your own information.

## 🎨 Quick Customization

### 1. Personal Information
Edit the file `src/config/portfolioConfig.ts` to update your personal details:

```typescript
personal: {
  name: "Your Name",                    // Replace with your full name
  title: "Your Professional Title",     // Your job title or role
  description: "Your description...",   // Brief description about yourself
  email: "<EMAIL>",     // Your email address
  phone: "+****************",          // Your phone number
  location: "Your City, Country",       // Your location
  avatar: "👨‍💻",                        // Emoji or image URL for avatar
}
```

### 2. About Section
Update your about description and statistics:

```typescript
about: {
  description: [
    "First paragraph about yourself...",
    "Second paragraph about your interests..."
  ],
  stats: [
    { number: "50+", label: "Projects Completed" },
    { number: "3+", label: "Years Experience" },
    { number: "20+", label: "Happy Clients" }
  ]
}
```

### 3. Skills
Customize your skills and proficiency levels:

```typescript
skills: [
  { name: 'JavaScript', level: 90, category: 'Frontend' },
  { name: 'React', level: 85, category: 'Frontend' },
  // Add more skills...
]
```

**Categories available:** Frontend, Backend, Database, Tools

### 4. Projects
Add your own projects:

```typescript
projects: [
  {
    id: 1,
    title: 'Your Project Name',
    description: 'Project description...',
    technologies: ['React', 'Node.js', 'MongoDB'],
    image: '🚀',  // Emoji or image URL
    liveUrl: 'https://your-project.com',
    githubUrl: 'https://github.com/username/project',
    category: 'Web App'  // Web App, Frontend, Mobile, etc.
  }
]
```

### 5. Social Links
Update your social media profiles:

```typescript
social: {
  linkedin: "https://linkedin.com/in/yourusername",
  github: "https://github.com/yourusername",
  twitter: "https://twitter.com/yourusername",
  instagram: "https://instagram.com/yourusername",
}
```

## 🎨 Advanced Customization

### Colors and Styling
The main color scheme is defined using CSS custom properties. To change colors:

1. Open `src/App.css`
2. Look for the gradient definitions:
   ```css
   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   ```
3. Replace `#667eea` and `#764ba2` with your preferred colors

### Adding Your Photo
1. Add your photo to the `public` folder
2. Update the avatar in `portfolioConfig.ts`:
   ```typescript
   avatar: "/your-photo.jpg"
   ```
3. Update the CSS in `src/components/Hero.css` to display images instead of emojis

### Custom Fonts
The portfolio uses Inter font from Google Fonts. To change:

1. Update the import in `src/index.css`:
   ```css
   @import url('https://fonts.googleapis.com/css2?family=YourFont:wght@300;400;500;600;700;800;900&display=swap');
   ```
2. Update the font-family in the body selector

## 🚀 Running the Portfolio

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## 📱 Responsive Design

The portfolio is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## 🌟 Features

- ✅ Fully responsive design
- ✅ Smooth scrolling navigation
- ✅ Animated skill bars
- ✅ Project filtering
- ✅ Contact form
- ✅ Modern gradient design
- ✅ TypeScript support
- ✅ Easy customization

## 📝 Adding New Sections

To add a new section:

1. Create a new component in `src/components/`
2. Add the component to `src/App.tsx`
3. Add navigation item to `portfolioConfig.ts`
4. Style the component with CSS

## 🎯 Tips for Best Results

1. **High-quality project descriptions**: Write clear, concise descriptions of your projects
2. **Accurate skill levels**: Be honest about your skill proficiency levels
3. **Professional photo**: Use a high-quality, professional headshot
4. **Working links**: Ensure all project and social links are functional
5. **Regular updates**: Keep your portfolio updated with new projects and skills

## 🔧 Troubleshooting

**Issue: Components not updating after config changes**
- Solution: Restart the development server (`npm start`)

**Issue: Images not loading**
- Solution: Ensure images are in the `public` folder and paths are correct

**Issue: Styling issues on mobile**
- Solution: Test on actual devices, not just browser dev tools

## 📞 Support

If you need help customizing your portfolio, refer to the React documentation or create an issue in the project repository.

Happy coding! 🚀
