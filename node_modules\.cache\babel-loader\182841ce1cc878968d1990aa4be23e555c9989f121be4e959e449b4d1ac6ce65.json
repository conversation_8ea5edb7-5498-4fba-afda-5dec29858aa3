{"ast": null, "code": "import React,{useState}from'react';import'./App.css';import LoadingScreen from'./components/LoadingScreen';import ScrollProgress from'./components/ScrollProgress';import CursorFollower from'./components/CursorFollower';import AccessibilityHelper from'./components/AccessibilityHelper';import Header from'./components/Header';import Hero from'./components/Hero';import About from'./components/About';import Skills from'./components/Skills';import Experience from'./components/Experience';import Certificates from'./components/Certificates';import Projects from'./components/Projects';import Contact from'./components/Contact';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function App(){const[isLoading,setIsLoading]=useState(true);const handleLoadingComplete=()=>{setIsLoading(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[isLoading&&/*#__PURE__*/_jsx(LoadingScreen,{onLoadingComplete:handleLoadingComplete}),!isLoading&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(CursorFollower,{}),/*#__PURE__*/_jsx(ScrollProgress,{}),/*#__PURE__*/_jsx(AccessibilityHelper,{}),/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsx(Hero,{}),/*#__PURE__*/_jsx(About,{}),/*#__PURE__*/_jsx(Skills,{}),/*#__PURE__*/_jsx(Experience,{}),/*#__PURE__*/_jsx(Certificates,{}),/*#__PURE__*/_jsx(Projects,{}),/*#__PURE__*/_jsx(Contact,{})]})]})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "LoadingScreen", "ScrollProgress", "Cursor<PERSON><PERSON>ow<PERSON>", "AccessibilityHelper", "Header", "Hero", "About", "Skills", "Experience", "Certificates", "Projects", "Contact", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "App", "isLoading", "setIsLoading", "handleLoadingComplete", "className", "children", "onLoadingComplete"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './App.css';\nimport LoadingScreen from './components/LoadingScreen';\nimport ScrollProgress from './components/ScrollProgress';\nimport CursorFollower from './components/CursorFollower';\nimport AccessibilityHelper from './components/AccessibilityHelper';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Experience from './components/Experience';\nimport Certificates from './components/Certificates';\nimport Projects from './components/Projects';\nimport Contact from './components/Contact';\n\nfunction App() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n\n  return (\n    <div className=\"App\">\n      {isLoading && <LoadingScreen onLoadingComplete={handleLoadingComplete} />}\n\n      {!isLoading && (\n        <>\n          <CursorFollower />\n          <ScrollProgress />\n          <AccessibilityHelper />\n          <Header />\n          <main>\n            <Hero />\n            <About />\n            <Skills />\n            <Experience />\n            <Certificates />\n            <Projects />\n            <Contact />\n          </main>\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,WAAW,CAClB,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,mBAAmB,KAAM,kCAAkC,CAClE,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,IAAI,KAAM,mBAAmB,CACpC,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAEhD,KAAM,CAAAsB,qBAAqB,CAAGA,CAAA,GAAM,CAClCD,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,mBACEL,KAAA,QAAKO,SAAS,CAAC,KAAK,CAAAC,QAAA,EACjBJ,SAAS,eAAIN,IAAA,CAACb,aAAa,EAACwB,iBAAiB,CAAEH,qBAAsB,CAAE,CAAC,CAExE,CAACF,SAAS,eACTJ,KAAA,CAAAE,SAAA,EAAAM,QAAA,eACEV,IAAA,CAACX,cAAc,GAAE,CAAC,cAClBW,IAAA,CAACZ,cAAc,GAAE,CAAC,cAClBY,IAAA,CAACV,mBAAmB,GAAE,CAAC,cACvBU,IAAA,CAACT,MAAM,GAAE,CAAC,cACVW,KAAA,SAAAQ,QAAA,eACEV,IAAA,CAACR,IAAI,GAAE,CAAC,cACRQ,IAAA,CAACP,KAAK,GAAE,CAAC,cACTO,IAAA,CAACN,MAAM,GAAE,CAAC,cACVM,IAAA,CAACL,UAAU,GAAE,CAAC,cACdK,IAAA,CAACJ,YAAY,GAAE,CAAC,cAChBI,IAAA,CAACH,QAAQ,GAAE,CAAC,cACZG,IAAA,CAACF,OAAO,GAAE,CAAC,EACP,CAAC,EACP,CACH,EACE,CAAC,CAEV,CAEA,cAAe,CAAAO,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}