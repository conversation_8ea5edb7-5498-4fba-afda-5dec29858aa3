{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./LoadingScreen.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoadingScreen=_ref=>{let{onLoadingComplete}=_ref;const[progress,setProgress]=useState(0);const[isComplete,setIsComplete]=useState(false);useEffect(()=>{const timer=setInterval(()=>{setProgress(prevProgress=>{if(prevProgress>=100){clearInterval(timer);setIsComplete(true);setTimeout(()=>{onLoadingComplete();},800);return 100;}return prevProgress+Math.random()*15;});},100);return()=>clearInterval(timer);},[onLoadingComplete]);return/*#__PURE__*/_jsxs(\"div\",{className:`loading-screen ${isComplete?'fade-out':''}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"loading-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"loading-logo\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Saran\"}),/*#__PURE__*/_jsx(\"div\",{className:\"logo-underline\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"loading-progress\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-fill\",style:{width:`${Math.min(progress,100)}%`}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"progress-text\",children:[Math.round(Math.min(progress,100)),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-subtitle\",children:\"Crafting Digital Experiences\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-particles\",children:[...Array(20)].map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:`particle particle-${i+1}`},i))})]});};export default LoadingScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingScreen", "_ref", "onLoadingComplete", "progress", "setProgress", "isComplete", "setIsComplete", "timer", "setInterval", "prevProgress", "clearInterval", "setTimeout", "Math", "random", "className", "children", "style", "width", "min", "round", "Array", "map", "_", "i"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/LoadingScreen.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './LoadingScreen.css';\n\ninterface LoadingScreenProps {\n  onLoadingComplete: () => void;\n}\n\nconst LoadingScreen: React.FC<LoadingScreenProps> = ({ onLoadingComplete }) => {\n  const [progress, setProgress] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setProgress((prevProgress) => {\n        if (prevProgress >= 100) {\n          clearInterval(timer);\n          setIsComplete(true);\n          setTimeout(() => {\n            onLoadingComplete();\n          }, 800);\n          return 100;\n        }\n        return prevProgress + Math.random() * 15;\n      });\n    }, 100);\n\n    return () => clearInterval(timer);\n  }, [onLoadingComplete]);\n\n  return (\n    <div className={`loading-screen ${isComplete ? 'fade-out' : ''}`}>\n      <div className=\"loading-content\">\n        <div className=\"loading-logo\">\n          <h1>Saran</h1>\n          <div className=\"logo-underline\"></div>\n        </div>\n        \n        <div className=\"loading-progress\">\n          <div className=\"progress-bar\">\n            <div \n              className=\"progress-fill\"\n              style={{ width: `${Math.min(progress, 100)}%` }}\n            ></div>\n          </div>\n          <div className=\"progress-text\">\n            {Math.round(Math.min(progress, 100))}%\n          </div>\n        </div>\n\n        <div className=\"loading-subtitle\">\n          Crafting Digital Experiences\n        </div>\n      </div>\n\n      <div className=\"loading-particles\">\n        {[...Array(20)].map((_, i) => (\n          <div key={i} className={`particle particle-${i + 1}`}></div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM7B,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,iBAAkB,CAAC,CAAAD,IAAA,CACxE,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACW,UAAU,CAAEC,aAAa,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAY,KAAK,CAAGC,WAAW,CAAC,IAAM,CAC9BJ,WAAW,CAAEK,YAAY,EAAK,CAC5B,GAAIA,YAAY,EAAI,GAAG,CAAE,CACvBC,aAAa,CAACH,KAAK,CAAC,CACpBD,aAAa,CAAC,IAAI,CAAC,CACnBK,UAAU,CAAC,IAAM,CACfT,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACP,MAAO,IAAG,CACZ,CACA,MAAO,CAAAO,YAAY,CAAGG,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAC1C,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAMH,aAAa,CAACH,KAAK,CAAC,CACnC,CAAC,CAAE,CAACL,iBAAiB,CAAC,CAAC,CAEvB,mBACEH,KAAA,QAAKe,SAAS,CAAE,kBAAkBT,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAU,QAAA,eAC/DhB,KAAA,QAAKe,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhB,KAAA,QAAKe,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlB,IAAA,OAAAkB,QAAA,CAAI,OAAK,CAAI,CAAC,cACdlB,IAAA,QAAKiB,SAAS,CAAC,gBAAgB,CAAM,CAAC,EACnC,CAAC,cAENf,KAAA,QAAKe,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BlB,IAAA,QAAKiB,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BlB,IAAA,QACEiB,SAAS,CAAC,eAAe,CACzBE,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGL,IAAI,CAACM,GAAG,CAACf,QAAQ,CAAE,GAAG,CAAC,GAAI,CAAE,CAC5C,CAAC,CACJ,CAAC,cACNJ,KAAA,QAAKe,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BH,IAAI,CAACO,KAAK,CAACP,IAAI,CAACM,GAAG,CAACf,QAAQ,CAAE,GAAG,CAAC,CAAC,CAAC,GACvC,EAAK,CAAC,EACH,CAAC,cAENN,IAAA,QAAKiB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,8BAElC,CAAK,CAAC,EACH,CAAC,cAENlB,IAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC/B,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,CAAEC,CAAC,gBACvB1B,IAAA,QAAaiB,SAAS,CAAE,qBAAqBS,CAAC,CAAG,CAAC,EAAG,EAA3CA,CAAiD,CAC5D,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}