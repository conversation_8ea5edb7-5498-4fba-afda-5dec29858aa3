{"ast": null, "code": "import React from'react';import'./Skills.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Skills=()=>{const skills=portfolioConfig.skills;const categories=['Frontend','Backend','Database','Cloud','DevOps','Tools'];return/*#__PURE__*/_jsx(\"section\",{id:\"skills\",className:\"skills\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"Skills & Technologies\"}),/*#__PURE__*/_jsx(\"div\",{className:\"skills-content\",children:categories.map(category=>/*#__PURE__*/_jsxs(\"div\",{className:\"skill-category\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"category-title\",children:category}),/*#__PURE__*/_jsx(\"div\",{className:\"skills-list\",children:skills.filter(skill=>skill.category===category).map(skill=>/*#__PURE__*/_jsxs(\"div\",{className:\"skill-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"skill-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"skill-name\",children:skill.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"skill-percentage\",children:[skill.level,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"skill-bar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"skill-progress\",style:{width:`${skill.level}%`}})})]},skill.name))})]},category))})]})});};export default Skills;", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Skills", "skills", "categories", "id", "className", "children", "map", "category", "filter", "skill", "name", "level", "style", "width"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Skills.tsx"], "sourcesContent": ["import React from 'react';\nimport './Skills.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface Skill {\n  name: string;\n  level: number;\n  category: string;\n}\n\nconst Skills: React.FC = () => {\n  const skills: Skill[] = portfolioConfig.skills;\n\n  const categories = ['Frontend', 'Backend', 'Database', 'Cloud', 'DevOps', 'Tools'];\n\n  return (\n    <section id=\"skills\" className=\"skills\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Skills & Technologies</h2>\n        <div className=\"skills-content\">\n          {categories.map((category) => (\n            <div key={category} className=\"skill-category\">\n              <h3 className=\"category-title\">{category}</h3>\n              <div className=\"skills-list\">\n                {skills\n                  .filter((skill) => skill.category === category)\n                  .map((skill) => (\n                    <div key={skill.name} className=\"skill-item\">\n                      <div className=\"skill-header\">\n                        <span className=\"skill-name\">{skill.name}</span>\n                        <span className=\"skill-percentage\">{skill.level}%</span>\n                      </div>\n                      <div className=\"skill-bar\">\n                        <div \n                          className=\"skill-progress\"\n                          style={{ width: `${skill.level}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,cAAc,CACrB,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ5D,KAAM,CAAAC,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,MAAe,CAAGN,eAAe,CAACM,MAAM,CAE9C,KAAM,CAAAC,UAAU,CAAG,CAAC,UAAU,CAAE,SAAS,CAAE,UAAU,CAAE,OAAO,CAAE,QAAQ,CAAE,OAAO,CAAC,CAElF,mBACEL,IAAA,YAASM,EAAE,CAAC,QAAQ,CAACC,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrCN,KAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBR,IAAA,OAAIO,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACxDR,IAAA,QAAKO,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5BH,UAAU,CAACI,GAAG,CAAEC,QAAQ,eACvBR,KAAA,QAAoBK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC5CR,IAAA,OAAIO,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEE,QAAQ,CAAK,CAAC,cAC9CV,IAAA,QAAKO,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBJ,MAAM,CACJO,MAAM,CAAEC,KAAK,EAAKA,KAAK,CAACF,QAAQ,GAAKA,QAAQ,CAAC,CAC9CD,GAAG,CAAEG,KAAK,eACTV,KAAA,QAAsBK,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC1CN,KAAA,QAAKK,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BR,IAAA,SAAMO,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEI,KAAK,CAACC,IAAI,CAAO,CAAC,cAChDX,KAAA,SAAMK,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAEI,KAAK,CAACE,KAAK,CAAC,GAAC,EAAM,CAAC,EACrD,CAAC,cACNd,IAAA,QAAKO,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBR,IAAA,QACEO,SAAS,CAAC,gBAAgB,CAC1BQ,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGJ,KAAK,CAACE,KAAK,GAAI,CAAE,CAChC,CAAC,CACJ,CAAC,GAVEF,KAAK,CAACC,IAWX,CACN,CAAC,CACD,CAAC,GAnBEH,QAoBL,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}