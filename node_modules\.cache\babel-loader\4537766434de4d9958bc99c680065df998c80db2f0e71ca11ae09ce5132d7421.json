{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\";\nimport React from 'react';\nimport './Skills.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Skills = () => {\n  const skills = portfolioConfig.skills;\n  const categories = ['Frontend', 'Backend', 'Database', 'Cloud', 'DevOps', 'Tools'];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"skills\",\n    className: \"skills\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Skills & Technologies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"skills-content\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skill-category\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"category-title\",\n            children: category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skills-list\",\n            children: skills.filter(skill => skill.category === category).map(skill => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skill-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-name\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-percentage\",\n                  children: [skill.level, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skill-bar\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"skill-progress\",\n                  style: {\n                    width: `${skill.level}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 23\n              }, this)]\n            }, skill.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 15\n          }, this)]\n        }, category, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = Skills;\nexport default Skills;\nvar _c;\n$RefreshReg$(_c, \"Skills\");", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsxDEV", "_jsxDEV", "Skills", "skills", "categories", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "category", "filter", "skill", "name", "level", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Skills.tsx"], "sourcesContent": ["import React from 'react';\nimport './Skills.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface Skill {\n  name: string;\n  level: number;\n  category: string;\n}\n\nconst Skills: React.FC = () => {\n  const skills: Skill[] = portfolioConfig.skills;\n\n  const categories = ['Frontend', 'Backend', 'Database', 'Cloud', 'DevOps', 'Tools'];\n\n  return (\n    <section id=\"skills\" className=\"skills\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Skills & Technologies</h2>\n        <div className=\"skills-content\">\n          {categories.map((category) => (\n            <div key={category} className=\"skill-category\">\n              <h3 className=\"category-title\">{category}</h3>\n              <div className=\"skills-list\">\n                {skills\n                  .filter((skill) => skill.category === category)\n                  .map((skill) => (\n                    <div key={skill.name} className=\"skill-item\">\n                      <div className=\"skill-header\">\n                        <span className=\"skill-name\">{skill.name}</span>\n                        <span className=\"skill-percentage\">{skill.level}%</span>\n                      </div>\n                      <div className=\"skill-bar\">\n                        {/* Inline style required for dynamic skill level percentage */}\n                        <div\n                          className=\"skill-progress\"\n                          style={{ width: `${skill.level}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AACrB,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5D,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMC,MAAe,GAAGJ,eAAe,CAACI,MAAM;EAE9C,MAAMC,UAAU,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;EAElF,oBACEH,OAAA;IAASI,EAAE,EAAC,QAAQ;IAACC,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACrCN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBN,OAAA;QAAIK,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDV,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BH,UAAU,CAACQ,GAAG,CAAEC,QAAQ,iBACvBZ,OAAA;UAAoBK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC5CN,OAAA;YAAIK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEM;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CV,OAAA;YAAKK,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBJ,MAAM,CACJW,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAACF,QAAQ,KAAKA,QAAQ,CAAC,CAC9CD,GAAG,CAAEG,KAAK,iBACTd,OAAA;cAAsBK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC1CN,OAAA;gBAAKK,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BN,OAAA;kBAAMK,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEQ,KAAK,CAACC;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChDV,OAAA;kBAAMK,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAEQ,KAAK,CAACE,KAAK,EAAC,GAAC;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNV,OAAA;gBAAKK,SAAS,EAAC,WAAW;gBAAAC,QAAA,eAExBN,OAAA;kBACEK,SAAS,EAAC,gBAAgB;kBAC1BY,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGJ,KAAK,CAACE,KAAK;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAXEI,KAAK,CAACC,IAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GApBEE,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACS,EAAA,GAtCIlB,MAAgB;AAwCtB,eAAeA,MAAM;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}