.projects {
  padding: var(--space-24) 0;
  background: var(--bg-tertiary);
  position: relative;
}

.projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(6, 182, 212, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 70% 80%, rgba(99, 102, 241, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 90% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 60%);
  pointer-events: none;
}

.projects::after {
  content: '';
  position: absolute;
  top: 40%;
  right: 20%;
  width: 300px;
  height: 300px;
  background: var(--gradient-warning);
  border-radius: 50%;
  filter: blur(120px);
  opacity: 0.06;
  animation: float 20s ease-in-out infinite;
}

.projects-header {
  text-align: center;
  margin-bottom: var(--space-16);
  position: relative;
  z-index: 2;
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-top: var(--space-4);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.project-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.project-card {
  background: var(--bg-elevated);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(6, 182, 212, 0.2);
  transition: all var(--duration-normal) var(--ease-out);
  animation: fadeInUp 1s ease-out;
  position: relative;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
  z-index: 1;
}

.project-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-cosmic);
  border-radius: var(--radius-2xl);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.project-card:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-xl), var(--glow-accent);
  border-color: var(--accent-light);
}

.project-card:hover::before {
  opacity: 0.05;
}

.project-card:hover::after {
  opacity: 0.4;
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 20px 20px;
}

.project-emoji {
  font-size: 4rem;
  z-index: 1;
  position: relative;
}

.project-content {
  padding: 2rem;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
}

.project-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tech-tag {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #e2e8f0;
}

.project-links {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-out);
  text-align: center;
  flex: 1;
}

.project-link.primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  border: 1px solid var(--primary);
  position: relative;
  overflow: hidden;
}

.project-link.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.project-link.primary:hover::before {
  left: 100%;
}

.project-link.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-primary);
}

.project-link.secondary {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--gray-300);
}

.project-link.secondary:hover {
  background: var(--gray-100);
  color: var(--text-primary);
  border-color: var(--gray-400);
  transform: translateY(-2px);
}

.project-link svg {
  transition: transform var(--duration-normal) var(--ease-out);
}

.project-link:hover svg {
  transform: translateX(2px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for project cards */
.project-card:nth-child(1) {
  animation-delay: 0s;
}

.project-card:nth-child(2) {
  animation-delay: 0.2s;
}

.project-card:nth-child(3) {
  animation-delay: 0.4s;
}

.project-card:nth-child(4) {
  animation-delay: 0.6s;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .projects {
    padding: 3rem 0;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .project-content {
    padding: 1.5rem;
  }

  .project-title {
    font-size: 1.3rem;
  }

  .project-links {
    flex-direction: column;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}
