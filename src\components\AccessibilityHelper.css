.accessibility-toggle {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 0 25px 25px 0;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
}

.accessibility-toggle:hover {
  transform: translateY(-50%) translateX(5px);
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
}

.accessibility-toggle:focus {
  outline: 3px solid #ffd89b;
  outline-offset: 2px;
}

.accessibility-panel {
  position: fixed;
  top: 0;
  left: -350px;
  width: 350px;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1002;
  overflow-y: auto;
}

.accessibility-panel.open {
  left: 0;
}

.accessibility-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.accessibility-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-btn:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.accessibility-content {
  padding: 1.5rem;
}

.accessibility-group {
  margin-bottom: 2rem;
}

.accessibility-group h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.font-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.font-controls button {
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.font-controls button:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.font-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.font-controls button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.font-size-display {
  padding: 0.5rem 1rem;
  background: #f7fafc;
  border-radius: 6px;
  font-weight: 600;
  color: #2d3748;
  min-width: 60px;
  text-align: center;
}

.accessibility-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2d3748;
}

.accessibility-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.2s ease;
}

.accessibility-checkbox input:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.accessibility-checkbox input:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.accessibility-checkbox input:focus + .checkmark {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.option-description {
  margin: 0;
  font-size: 0.875rem;
  color: #718096;
  margin-left: 2.75rem;
  line-height: 1.4;
}

.accessibility-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

/* High contrast mode styles */
:root.high-contrast {
  --primary-gradient: linear-gradient(135deg, #000000 0%, #333333 100%);
  --text-primary: #000000;
  --text-secondary: #000000;
  --bg-primary: #ffffff;
  --bg-secondary: #ffffff;
}

:root.high-contrast .hero {
  background: #000000 !important;
  color: #ffffff !important;
}

:root.high-contrast .hero-title,
:root.high-contrast .hero-subtitle,
:root.high-contrast .hero-description {
  color: #ffffff !important;
}

/* Reduced motion styles */
:root.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Mobile styles */
@media (max-width: 768px) {
  .accessibility-panel {
    width: 100%;
    left: -100%;
  }
  
  .accessibility-toggle {
    width: 45px;
    height: 45px;
  }
}
