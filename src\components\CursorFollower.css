/* Hide default cursor on desktop */
@media (min-width: 769px) {
  * {
    cursor: none !important;
  }
}

.cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  pointer-events: none;
  z-index: 9998;
  transform: translate(-50%, -50%);
  transition: all 0.1s ease;
  mix-blend-mode: difference;
}

.cursor-inner {
  width: 100%;
  height: 100%;
  border: 2px solid white;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.cursor-inner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.cursor-trail {
  position: fixed;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9997;
  transform: translate(-50%, -50%);
  transition: all 0.15s ease;
  opacity: 0.8;
}

/* Hover state */
.cursor-follower.hovering {
  transform: translate(-50%, -50%) scale(1.5);
}

.cursor-follower.hovering .cursor-inner {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.cursor-follower.hovering .cursor-inner::before {
  background: #667eea;
  transform: translate(-50%, -50%) scale(1.5);
}

/* Clicking state */
.cursor-follower.clicking {
  transform: translate(-50%, -50%) scale(0.8);
}

.cursor-follower.clicking .cursor-inner {
  border-color: #764ba2;
  background: rgba(118, 75, 162, 0.2);
}

.cursor-follower.clicking .cursor-inner::before {
  background: #764ba2;
  transform: translate(-50%, -50%) scale(2);
}

/* Pulse animation for interactive elements */
.cursor-follower.hovering .cursor-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid #667eea;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: cursorPulse 1.5s ease-in-out infinite;
}

@keyframes cursorPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Mobile - hide cursor follower */
@media (max-width: 768px) {
  .cursor-follower,
  .cursor-trail {
    display: none;
  }
  
  * {
    cursor: auto !important;
  }
}
