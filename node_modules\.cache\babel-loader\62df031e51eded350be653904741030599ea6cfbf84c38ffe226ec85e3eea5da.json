{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./Header.css';import{portfolioConfig}from'../config/portfolioConfig';import DarkModeToggle from'./DarkModeToggle';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Header=()=>{const[isScrolled,setIsScrolled]=useState(false);const[isMobileMenuOpen,setIsMobileMenuOpen]=useState(false);useEffect(()=>{const handleScroll=()=>{setIsScrolled(window.scrollY>50);};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);const scrollToSection=sectionId=>{const element=document.getElementById(sectionId);if(element){element.scrollIntoView({behavior:'smooth'});setIsMobileMenuOpen(false);}};return/*#__PURE__*/_jsx(\"header\",{className:`header ${isScrolled?'scrolled':''}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"header-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo\",children:/*#__PURE__*/_jsx(\"h2\",{children:\"Portfolio\"})}),/*#__PURE__*/_jsx(\"nav\",{className:`nav ${isMobileMenuOpen?'nav-open':''}`,children:/*#__PURE__*/_jsx(\"ul\",{children:portfolioConfig.navigation.map(item=>/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>scrollToSection(item.id),children:item.name})},item.id))})}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsx(DarkModeToggle,{}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",className:`mobile-menu-toggle ${isMobileMenuOpen?'active':''}`,onClick:()=>setIsMobileMenuOpen(!isMobileMenuOpen),\"aria-label\":\"Toggle mobile menu\",children:[/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"span\",{})]})]})]})});};export default Header;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "portfolioConfig", "DarkModeToggle", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "isScrolled", "setIsScrolled", "isMobileMenuOpen", "setIsMobileMenuOpen", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "className", "children", "navigation", "map", "item", "type", "onClick", "id", "name"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Header.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Header.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport DarkModeToggle from './DarkModeToggle';\n\nconst Header: React.FC = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      setIsMobileMenuOpen(false);\n    }\n  };\n\n  return (\n    <header className={`header ${isScrolled ? 'scrolled' : ''}`}>\n      <div className=\"header-container\">\n        <div className=\"logo\">\n          <h2>Portfolio</h2>\n        </div>\n        \n        <nav className={`nav ${isMobileMenuOpen ? 'nav-open' : ''}`}>\n          <ul>\n            {portfolioConfig.navigation.map((item) => (\n              <li key={item.id}>\n                <button type=\"button\" onClick={() => scrollToSection(item.id)}>\n                  {item.name}\n                </button>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        <div className=\"header-actions\">\n          <DarkModeToggle />\n          <button\n            type=\"button\"\n            className={`mobile-menu-toggle ${isMobileMenuOpen ? 'active' : ''}`}\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            aria-label=\"Toggle mobile menu\"\n          >\n            <span></span>\n            <span></span>\n            <span></span>\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,cAAc,CACrB,OAASC,eAAe,KAAQ,2BAA2B,CAC3D,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAAC,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGV,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACW,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAE/DC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAY,YAAY,CAAGA,CAAA,GAAM,CACzBH,aAAa,CAACI,MAAM,CAACC,OAAO,CAAG,EAAE,CAAC,CACpC,CAAC,CAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAC/C,MAAO,IAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,CAAEJ,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAK,eAAe,CAAIC,SAAiB,EAAK,CAC7C,KAAM,CAAAC,OAAO,CAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC,CAClD,GAAIC,OAAO,CAAE,CACXA,OAAO,CAACG,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAC9CZ,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAED,mBACEP,IAAA,WAAQoB,SAAS,CAAE,UAAUhB,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAiB,QAAA,cAC1DnB,KAAA,QAAKkB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BrB,IAAA,QAAKoB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrB,IAAA,OAAAqB,QAAA,CAAI,WAAS,CAAI,CAAC,CACf,CAAC,cAENrB,IAAA,QAAKoB,SAAS,CAAE,OAAOd,gBAAgB,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAe,QAAA,cAC1DrB,IAAA,OAAAqB,QAAA,CACGxB,eAAe,CAACyB,UAAU,CAACC,GAAG,CAAEC,IAAI,eACnCxB,IAAA,OAAAqB,QAAA,cACErB,IAAA,WAAQyB,IAAI,CAAC,QAAQ,CAACC,OAAO,CAAEA,CAAA,GAAMb,eAAe,CAACW,IAAI,CAACG,EAAE,CAAE,CAAAN,QAAA,CAC3DG,IAAI,CAACI,IAAI,CACJ,CAAC,EAHFJ,IAAI,CAACG,EAIV,CACL,CAAC,CACA,CAAC,CACF,CAAC,cAENzB,KAAA,QAAKkB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BrB,IAAA,CAACF,cAAc,GAAE,CAAC,cAClBI,KAAA,WACEuB,IAAI,CAAC,QAAQ,CACbL,SAAS,CAAE,sBAAsBd,gBAAgB,CAAG,QAAQ,CAAG,EAAE,EAAG,CACpEoB,OAAO,CAAEA,CAAA,GAAMnB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE,CACtD,aAAW,oBAAoB,CAAAe,QAAA,eAE/BrB,IAAA,UAAY,CAAC,cACbA,IAAA,UAAY,CAAC,cACbA,IAAA,UAAY,CAAC,EACP,CAAC,EACN,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}