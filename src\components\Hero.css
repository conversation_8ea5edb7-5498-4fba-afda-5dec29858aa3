.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  clip-path: polygon(20% 0%, 100% 0%, 100% 100%, 0% 100%);
  z-index: 1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 2;
  min-height: 100vh;
}

.hero-content {
  opacity: 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;
}

.hero-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: clamp(var(--text-4xl), 8vw, var(--text-6xl));
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.highlight {
  color: var(--primary);
  position: relative;
}

.hero-subtitle {
  font-size: clamp(var(--text-lg), 3vw, var(--text-2xl));
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  letter-spacing: -0.011em;
}

.hero-description {
  font-size: clamp(var(--text-base), 2vw, var(--text-lg));
  color: var(--text-muted);
  margin-bottom: var(--space-8);
  line-height: 1.7;
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.hero-avatar {
  position: relative;
}

.avatar-placeholder {
  width: 320px;
  height: 320px;
  border-radius: var(--radius-3xl);
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8rem;
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.avatar-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  opacity: 0.1;
}

.hero-scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.scroll-arrow {
  cursor: pointer;
  font-size: var(--text-2xl);
  color: var(--text-muted);
  animation: bounce 2s infinite;
  transition: color var(--duration-normal) var(--ease-out);
  padding: var(--space-2);
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.scroll-arrow:hover {
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Clean Modern Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero::before {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
    padding: var(--space-4);
  }

  .hero-content {
    order: 2;
  }

  .hero-image {
    order: 1;
  }

  .avatar-placeholder {
    width: 240px;
    height: 240px;
    font-size: 6rem;
  }

  .hero-buttons {
    justify-content: center;
    gap: var(--space-3);
  }

  .hero-buttons .btn {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .hero-container {
    gap: var(--space-8);
  }

  .avatar-placeholder {
    width: 200px;
    height: 200px;
    font-size: 5rem;
  }

  .hero-buttons {
    flex-direction: column;
  }
}
