.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  background: var(--gradient-dark);
  clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);
  z-index: 1;
  opacity: 0.7;
}

.hero::after {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 300px;
  height: 300px;
  background: var(--gradient-primary);
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.1;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 2;
  min-height: 100vh;
}

.hero-content {
  opacity: 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-2) var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--gray-400);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  animation: slideInLeft 0.8s var(--ease-out) 0.4s both;
  box-shadow: var(--shadow-sm);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.hero-badge span {
  margin-left: var(--space-1);
}

.hero-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: clamp(var(--text-4xl), 8vw, var(--text-6xl));
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.highlight {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  font-weight: 700;
}

.hero-subtitle {
  font-size: clamp(var(--text-lg), 3vw, var(--text-2xl));
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  letter-spacing: -0.011em;
}

.hero-description {
  font-size: clamp(var(--text-base), 2vw, var(--text-lg));
  color: var(--text-muted);
  margin-bottom: var(--space-8);
  line-height: 1.7;
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: var(--space-8);
  margin: var(--space-8) 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.6s both;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: var(--text-xs);
  color: var(--text-muted);
  margin-top: var(--space-1);
  font-weight: 500;
}

.hero-buttons {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  animation: slideInLeft 0.8s var(--ease-out) 0.8s both;
}

.hero-buttons .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.hero-buttons .btn svg {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hero-buttons .btn:hover svg {
  transform: translateX(2px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.hero-avatar {
  position: relative;
}

.avatar-placeholder {
  width: 320px;
  height: 320px;
  border-radius: var(--radius-3xl);
  background: var(--gradient-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8rem;
  box-shadow: var(--shadow-xl), var(--glow-primary);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--gray-400);
  animation: float 6s ease-in-out infinite;
}

.avatar-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.1;
}

.avatar-placeholder::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

.hero-scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.scroll-arrow {
  cursor: pointer;
  font-size: var(--text-2xl);
  color: var(--text-muted);
  animation: bounce 2s infinite;
  transition: color var(--duration-normal) var(--ease-out);
  padding: var(--space-2);
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.scroll-arrow:hover {
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Clean Modern Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero::before {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
    padding: var(--space-4);
  }

  .hero-content {
    order: 2;
  }

  .hero-image {
    order: 1;
  }

  .avatar-placeholder {
    width: 240px;
    height: 240px;
    font-size: 6rem;
  }

  .hero-stats {
    gap: var(--space-6);
    margin: var(--space-6) 0;
  }

  .stat-number {
    font-size: var(--text-xl);
  }

  .hero-buttons {
    justify-content: center;
    gap: var(--space-3);
  }

  .hero-buttons .btn {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .hero-container {
    gap: var(--space-8);
  }

  .avatar-placeholder {
    width: 200px;
    height: 200px;
    font-size: 5rem;
  }

  .hero-stats {
    gap: var(--space-4);
    margin: var(--space-4) 0;
  }

  .stat-number {
    font-size: var(--text-lg);
  }

  .hero-buttons {
    flex-direction: column;
  }
}
