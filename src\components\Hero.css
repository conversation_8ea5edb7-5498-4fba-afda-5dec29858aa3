/* HERO SECTION - AI INTELLIGENCE THEME */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 30%, #0f1419 70%, #000000 100%);
  position: relative;
  overflow: hidden;
  border-bottom: 3px solid #0066ff;
}

/* AI Neural Network Background */
.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 15% 25%, rgba(0, 102, 255, 0.2) 0%, transparent 40%),
    radial-gradient(circle at 85% 75%, rgba(138, 43, 226, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 50% 50%, rgba(0, 255, 136, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 25% 75%, rgba(192, 192, 192, 0.05) 0%, transparent 40%);
  animation: aiPulse 8s ease-in-out infinite;
  z-index: 1;
}

/* AI Circuit Pattern Overlay */
.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 98%, rgba(0, 102, 255, 0.1) 100%),
    linear-gradient(0deg, transparent 98%, rgba(138, 43, 226, 0.08) 100%);
  background-size: 80px 80px;
  animation: aiCircuitFlow 20s linear infinite;
  z-index: 1;
  pointer-events: none;
}

/* AI Neural Network Nodes */
.hero-container::before {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 102, 255, 0.3) 0%, rgba(0, 102, 255, 0.1) 50%, transparent 100%);
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.8;
  z-index: 1;
  animation: aiNeuralPulse 6s ease-in-out infinite;
}

.hero-container::after {
  content: '';
  position: absolute;
  top: 60%;
  left: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.4) 0%, rgba(138, 43, 226, 0.1) 50%, transparent 100%);
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.7;
  z-index: 1;
  animation: aiNeuralPulse 8s ease-in-out infinite reverse;
}

/* Additional AI Processing Nodes */
.hero-content::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 70%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.3) 0%, rgba(0, 255, 136, 0.1) 50%, transparent 100%);
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.6;
  z-index: 1;
  animation: aiNeuralPulse 4s ease-in-out infinite;
}

.hero-content::after {
  content: '';
  position: absolute;
  top: 80%;
  right: 20%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(192, 192, 192, 0.3) 0%, rgba(192, 192, 192, 0.1) 50%, transparent 100%);
  border-radius: 50%;
  filter: blur(30px);
  opacity: 0.5;
  z-index: 1;
  animation: aiNeuralPulse 5s ease-in-out infinite reverse;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 2;
  min-height: 100vh;
}

.hero-content {
  opacity: 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  background: rgba(10, 10, 15, 0.9);
  border: 2px solid #0066ff;
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 600;
  color: #0066ff;
  margin-bottom: var(--space-6);
  animation: slideInLeft 0.8s var(--ease-out) 0.4s both;
  box-shadow: 0 0 20px rgba(0, 102, 255, 0.5), 0 0 40px rgba(0, 102, 255, 0.2);
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Courier New', monospace;
}

.hero-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 102, 255, 0.4), transparent);
  animation: aiDataFlow 3s ease-in-out infinite;
}

.hero-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #0066ff 0%, #8a2be2 50%, #00ff88 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.3;
  animation: aiNeuralPulse 4s ease-in-out infinite;
}

.hero-badge span {
  margin-left: var(--space-1);
}

.hero-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: clamp(var(--text-4xl), 8vw, var(--text-6xl));
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.highlight {
  background: linear-gradient(135deg, #0066ff 0%, #8a2be2 30%, #00ff88 60%, #0066ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  font-weight: 800;
  animation: aiHologram 4s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 102, 255, 0.6));
  text-shadow: 0 0 30px rgba(0, 102, 255, 0.4);
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
}

.highlight::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #8a2be2 0%, #00ff88 50%, #0066ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  z-index: -1;
  opacity: 0.4;
  animation: aiGlitch 6s ease-in-out infinite;
}

.highlight::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 102, 255, 0.1) 50%, transparent 100%);
  animation: aiScanLine 2s linear infinite;
  pointer-events: none;
}

.hero-subtitle {
  font-size: clamp(var(--text-lg), 3vw, var(--text-2xl));
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  letter-spacing: -0.011em;
}

.hero-description {
  font-size: clamp(var(--text-base), 2vw, var(--text-lg));
  color: var(--text-muted);
  margin-bottom: var(--space-8);
  line-height: 1.7;
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: var(--space-8);
  margin: var(--space-8) 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.6s both;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: var(--text-xs);
  color: var(--text-muted);
  margin-top: var(--space-1);
  font-weight: 500;
}

.hero-buttons {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  animation: slideInLeft 0.8s var(--ease-out) 0.8s both;
}

.hero-buttons .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.hero-buttons .btn svg {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hero-buttons .btn:hover svg {
  transform: translateX(2px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.hero-avatar {
  position: relative;
}

.avatar-placeholder {
  width: 350px;
  height: 350px;
  border-radius: var(--radius-3xl);
  background: var(--gradient-elevated);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8rem;
  box-shadow: var(--shadow-xl), var(--glow-primary);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(99, 102, 241, 0.3);
  animation: float 8s ease-in-out infinite;
}

.avatar-placeholder:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-xl), var(--glow-primary), 0 0 50px rgba(99, 102, 241, 0.4);
  border-color: var(--primary-light);
}

.avatar-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.1;
}

.avatar-placeholder::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.15), transparent);
  animation: shimmer 4s ease-in-out infinite;
}

.hero-scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.scroll-arrow {
  cursor: pointer;
  font-size: var(--text-2xl);
  color: var(--text-muted);
  animation: bounce 2s infinite;
  transition: color var(--duration-normal) var(--ease-out);
  padding: var(--space-2);
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.scroll-arrow:hover {
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Clean Modern Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* AI Theme Animations */
@keyframes aiPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes aiCircuitFlow {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(80px) translateY(80px);
  }
}

@keyframes aiNeuralPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
    filter: blur(80px);
  }
  33% {
    opacity: 1;
    transform: scale(1.1);
    filter: blur(60px);
  }
  66% {
    opacity: 0.6;
    transform: scale(0.9);
    filter: blur(100px);
  }
}

@keyframes aiDataFlow {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes aiHologram {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(0, 102, 255, 0.6));
    text-shadow: 0 0 30px rgba(0, 102, 255, 0.4);
  }
  25% {
    filter: drop-shadow(0 0 25px rgba(138, 43, 226, 0.6));
    text-shadow: 0 0 35px rgba(138, 43, 226, 0.4);
  }
  50% {
    filter: drop-shadow(0 0 30px rgba(0, 255, 136, 0.6));
    text-shadow: 0 0 40px rgba(0, 255, 136, 0.4);
  }
  75% {
    filter: drop-shadow(0 0 25px rgba(192, 192, 192, 0.6));
    text-shadow: 0 0 35px rgba(192, 192, 192, 0.4);
  }
}

@keyframes aiGlitch {
  0%, 90%, 100% {
    opacity: 0.4;
    transform: translateX(0);
  }
  5% {
    opacity: 0.7;
    transform: translateX(2px);
  }
  10% {
    opacity: 0.3;
    transform: translateX(-2px);
  }
  15% {
    opacity: 0.6;
    transform: translateX(1px);
  }
}

@keyframes aiScanLine {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero::before {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
    padding: var(--space-4);
  }

  .hero-content {
    order: 2;
  }

  .hero-image {
    order: 1;
  }

  .avatar-placeholder {
    width: 240px;
    height: 240px;
    font-size: 6rem;
  }

  .hero-stats {
    gap: var(--space-6);
    margin: var(--space-6) 0;
  }

  .stat-number {
    font-size: var(--text-xl);
  }

  .hero-buttons {
    justify-content: center;
    gap: var(--space-3);
  }

  .hero-buttons .btn {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .hero-container {
    gap: var(--space-8);
  }

  .avatar-placeholder {
    width: 200px;
    height: 200px;
    font-size: 5rem;
  }

  .hero-stats {
    gap: var(--space-4);
    margin: var(--space-4) 0;
  }

  .stat-number {
    font-size: var(--text-lg);
  }

  .hero-buttons {
    flex-direction: column;
  }
}
