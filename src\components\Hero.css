/* HERO SECTION - COMPLETE AI UNIVERSE THEME */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background:
    radial-gradient(ellipse at center, #0a0a0f 0%, #000000 100%),
    linear-gradient(135deg, #000814 0%, #001d3d 25%, #003566 50%, #001d3d 75%, #000814 100%);
  position: relative;
  overflow: hidden;
  border-bottom: 4px solid transparent;
  border-image: linear-gradient(90deg, #0066ff, #00ffff, #0066ff) 1;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
}

/* AI Digital Matrix Background */
.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    /* Neural Network Nodes */
    radial-gradient(circle at 10% 20%, rgba(0, 102, 255, 0.3) 0%, rgba(0, 102, 255, 0.1) 30%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(0, 255, 255, 0.25) 0%, rgba(0, 255, 255, 0.08) 30%, transparent 50%),
    radial-gradient(circle at 30% 70%, rgba(138, 43, 226, 0.2) 0%, rgba(138, 43, 226, 0.06) 30%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(0, 255, 136, 0.15) 0%, rgba(0, 255, 136, 0.05) 30%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(255, 0, 255, 0.1) 0%, rgba(255, 0, 255, 0.03) 30%, transparent 50%),
    /* Circuit Board Pattern */
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(0, 102, 255, 0.03) 2px,
      rgba(0, 102, 255, 0.03) 4px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 255, 0.02) 2px,
      rgba(0, 255, 255, 0.02) 4px
    );
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 50px 50px, 50px 50px;
  animation: aiNeuralActivity 12s ease-in-out infinite;
  z-index: 1;
}

/* AI Data Stream Overlay */
.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    /* Digital Rain Effect */
    linear-gradient(180deg, transparent 0%, rgba(0, 255, 136, 0.05) 50%, transparent 100%),
    linear-gradient(90deg, transparent 0%, rgba(0, 102, 255, 0.03) 50%, transparent 100%),
    /* Circuit Connections */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 100px,
      rgba(0, 255, 255, 0.02) 100px,
      rgba(0, 255, 255, 0.02) 102px
    );
  background-size: 20px 100%, 100% 20px, 200px 200px;
  animation: aiDataFlow 15s linear infinite;
  z-index: 2;
  pointer-events: none;
}

/* AI Neural Network Processing Nodes */
.hero-container::before {
  content: '';
  position: absolute;
  top: 15%;
  right: 8%;
  width: 400px;
  height: 400px;
  background:
    radial-gradient(circle at 30% 30%, rgba(0, 102, 255, 0.4) 0%, transparent 20%),
    radial-gradient(circle at 70% 70%, rgba(0, 255, 255, 0.3) 0%, transparent 25%),
    radial-gradient(circle at 50% 50%, rgba(0, 102, 255, 0.2) 0%, transparent 50%);
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.9;
  z-index: 1;
  animation: aiMainProcessor 10s ease-in-out infinite;
}

.hero-container::after {
  content: '';
  position: absolute;
  top: 65%;
  left: 10%;
  width: 250px;
  height: 250px;
  background:
    radial-gradient(circle at 40% 40%, rgba(138, 43, 226, 0.5) 0%, transparent 25%),
    radial-gradient(circle at 60% 60%, rgba(255, 0, 255, 0.3) 0%, transparent 30%),
    radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.2) 0%, transparent 50%);
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.8;
  z-index: 1;
  animation: aiSecondaryProcessor 12s ease-in-out infinite reverse;
}

/* AI Data Processing Centers */
.hero-content::before {
  content: '';
  position: absolute;
  top: 5%;
  left: 75%;
  width: 180px;
  height: 180px;
  background:
    radial-gradient(circle at 35% 35%, rgba(0, 255, 136, 0.4) 0%, transparent 20%),
    radial-gradient(circle at 65% 65%, rgba(0, 255, 200, 0.3) 0%, transparent 25%),
    radial-gradient(circle at 50% 50%, rgba(0, 255, 136, 0.2) 0%, transparent 50%);
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.7;
  z-index: 1;
  animation: aiDataProcessor 8s ease-in-out infinite;
}

.hero-content::after {
  content: '';
  position: absolute;
  top: 85%;
  right: 15%;
  width: 120px;
  height: 120px;
  background:
    radial-gradient(circle at 45% 45%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
    radial-gradient(circle at 55% 55%, rgba(192, 192, 192, 0.25) 0%, transparent 25%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.6;
  z-index: 1;
  animation: aiMemoryBank 6s ease-in-out infinite reverse;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 2;
  min-height: 100vh;
}

/* AI Content Container */
.hero-content {
  opacity: 0;
  animation: aiSystemBoot 2s var(--ease-out) 0.5s forwards;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  position: relative;
}

.hero-content::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 1px solid rgba(0, 102, 255, 0.2);
  border-radius: 10px;
  background:
    linear-gradient(45deg, transparent 30%, rgba(0, 102, 255, 0.02) 50%, transparent 70%),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 255, 0.01) 2px,
      rgba(0, 255, 255, 0.01) 4px
    );
  animation: aiInterfaceBorder 8s linear infinite;
  pointer-events: none;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  background: rgba(10, 10, 15, 0.9);
  border: 2px solid #0066ff;
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 600;
  color: #0066ff;
  margin-bottom: var(--space-6);
  animation: slideInLeft 0.8s var(--ease-out) 0.4s both;
  box-shadow: 0 0 20px rgba(0, 102, 255, 0.5), 0 0 40px rgba(0, 102, 255, 0.2);
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Courier New', monospace;
}

.hero-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 102, 255, 0.4), transparent);
  animation: aiDataFlow 3s ease-in-out infinite;
}

.hero-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #0066ff 0%, #8a2be2 50%, #00ff88 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.3;
  animation: aiNeuralPulse 4s ease-in-out infinite;
}

.hero-badge span {
  margin-left: var(--space-1);
}

/* AI Title System */
.hero-title {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: clamp(2.8rem, 8vw, 5.5rem);
  font-weight: 900;
  line-height: 1.05;
  margin-bottom: var(--space-6);
  color: #ffffff;
  letter-spacing: 4px;
  text-transform: uppercase;
  position: relative;
  animation: aiTitleReveal 2.5s var(--ease-out) 1s both;
  text-shadow:
    0 0 10px rgba(0, 102, 255, 0.8),
    0 0 20px rgba(0, 102, 255, 0.6),
    0 0 30px rgba(0, 102, 255, 0.4),
    0 0 40px rgba(0, 102, 255, 0.2);
}

.hero-title::before {
  content: '> INITIALIZING: ';
  color: #00ff88;
  font-size: 0.4em;
  display: block;
  margin-bottom: 10px;
  animation: aiTypewriter 1.5s steps(15) 0.5s both;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.hero-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 102, 255, 0.15) 50%, transparent 100%);
  animation: aiTitleScan 4s linear infinite;
  pointer-events: none;
}

.highlight {
  background: linear-gradient(135deg, #0066ff 0%, #8a2be2 30%, #00ff88 60%, #0066ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  font-weight: 800;
  animation: aiHologram 4s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 102, 255, 0.6));
  text-shadow: 0 0 30px rgba(0, 102, 255, 0.4);
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
}

.highlight::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #8a2be2 0%, #00ff88 50%, #0066ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  z-index: -1;
  opacity: 0.4;
  animation: aiGlitch 6s ease-in-out infinite;
}

.highlight::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 102, 255, 0.1) 50%, transparent 100%);
  animation: aiScanLine 2s linear infinite;
  pointer-events: none;
}

/* AI Subtitle System */
.hero-subtitle {
  font-size: clamp(var(--text-lg), 3vw, var(--text-2xl));
  font-weight: 600;
  color: #00ffff;
  margin-bottom: var(--space-6);
  letter-spacing: 2px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  text-transform: uppercase;
  animation: aiSubtitleGlow 3s ease-in-out infinite;
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
  position: relative;
}

.hero-subtitle::before {
  content: '// ';
  color: #666;
  animation: aiCursor 1.5s ease-in-out infinite;
}

/* AI Description System */
.hero-description {
  font-size: clamp(var(--text-base), 2vw, var(--text-lg));
  color: #b0b0b0;
  margin-bottom: var(--space-8);
  line-height: 1.8;
  max-width: 600px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  animation: aiTextStream 2s var(--ease-out) 1.5s both;
  position: relative;
  text-shadow: 0 0 5px rgba(176, 176, 176, 0.3);
}

.hero-description::before {
  content: '/* ';
  color: #555;
  font-style: italic;
}

.hero-description::after {
  content: ' */';
  color: #555;
  font-style: italic;
}

.hero-stats {
  display: flex;
  gap: var(--space-8);
  margin: var(--space-8) 0;
  animation: slideInLeft 0.8s var(--ease-out) 0.6s both;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: var(--text-xs);
  color: var(--text-muted);
  margin-top: var(--space-1);
  font-weight: 500;
}

.hero-buttons {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  animation: slideInLeft 0.8s var(--ease-out) 0.8s both;
}

.hero-buttons .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.hero-buttons .btn svg {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hero-buttons .btn:hover svg {
  transform: translateX(2px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;
}

.hero-avatar {
  position: relative;
}

.avatar-placeholder {
  width: 350px;
  height: 350px;
  border-radius: var(--radius-3xl);
  background: var(--gradient-elevated);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8rem;
  box-shadow: var(--shadow-xl), var(--glow-primary);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(99, 102, 241, 0.3);
  animation: float 8s ease-in-out infinite;
}

.avatar-placeholder:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-xl), var(--glow-primary), 0 0 50px rgba(99, 102, 241, 0.4);
  border-color: var(--primary-light);
}

.avatar-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.1;
}

.avatar-placeholder::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.15), transparent);
  animation: shimmer 4s ease-in-out infinite;
}

.hero-scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.scroll-arrow {
  cursor: pointer;
  font-size: var(--text-2xl);
  color: var(--text-muted);
  animation: bounce 2s infinite;
  transition: color var(--duration-normal) var(--ease-out);
  padding: var(--space-2);
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.scroll-arrow:hover {
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Clean Modern Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* COMPLETE AI ANIMATION SYSTEM */

/* Neural Network Activity */
@keyframes aiNeuralActivity {
  0%, 100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.02) rotate(0.5deg);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05) rotate(0deg);
  }
  75% {
    opacity: 0.7;
    transform: scale(1.03) rotate(-0.5deg);
  }
}

/* AI Data Stream Flow */
@keyframes aiDataFlow {
  0% {
    transform: translateX(-100%) translateY(-100%);
    opacity: 0;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
    transform: translateX(0%) translateY(0%);
  }
  75% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100%) translateY(100%);
    opacity: 0;
  }
}

/* AI Processing Nodes */
@keyframes aiMainProcessor {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1) rotate(0deg);
    filter: blur(100px) hue-rotate(0deg);
  }
  33% {
    opacity: 1;
    transform: scale(1.1) rotate(120deg);
    filter: blur(80px) hue-rotate(60deg);
  }
  66% {
    opacity: 0.7;
    transform: scale(0.95) rotate(240deg);
    filter: blur(120px) hue-rotate(120deg);
  }
}

@keyframes aiSecondaryProcessor {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1) rotate(0deg);
    filter: blur(80px) hue-rotate(0deg);
  }
  40% {
    opacity: 1;
    transform: scale(1.15) rotate(-90deg);
    filter: blur(60px) hue-rotate(-45deg);
  }
  80% {
    opacity: 0.6;
    transform: scale(0.9) rotate(-180deg);
    filter: blur(100px) hue-rotate(-90deg);
  }
}

@keyframes aiDataProcessor {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1) rotate(0deg);
    filter: blur(60px) hue-rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
    filter: blur(40px) hue-rotate(90deg);
  }
}

@keyframes aiMemoryBank {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1) rotate(0deg);
    filter: blur(40px) hue-rotate(0deg);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.1) rotate(-90deg);
    filter: blur(30px) hue-rotate(-45deg);
  }
}

/* AI System Boot Animation */
@keyframes aiSystemBoot {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  25% {
    opacity: 0.3;
    transform: translateY(15px) scale(0.98);
    filter: blur(3px);
  }
  50% {
    opacity: 0.6;
    transform: translateY(5px) scale(0.99);
    filter: blur(1px);
  }
  75% {
    opacity: 0.8;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

/* AI Interface Border Animation */
@keyframes aiInterfaceBorder {
  0%, 100% {
    border-color: rgba(0, 102, 255, 0.2);
    box-shadow: 0 0 10px rgba(0, 102, 255, 0.1);
  }
  25% {
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
  }
  50% {
    border-color: rgba(0, 255, 136, 0.25);
    box-shadow: 0 0 12px rgba(0, 255, 136, 0.15);
  }
  75% {
    border-color: rgba(138, 43, 226, 0.2);
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.1);
  }
}

/* AI Typography Animations */
@keyframes aiTitleReveal {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.8);
    filter: blur(10px);
    text-shadow: none;
  }
  25% {
    opacity: 0.3;
    transform: translateY(25px) scale(0.9);
    filter: blur(5px);
  }
  50% {
    opacity: 0.6;
    transform: translateY(10px) scale(0.95);
    filter: blur(2px);
  }
  75% {
    opacity: 0.8;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
    text-shadow:
      0 0 10px rgba(0, 102, 255, 0.8),
      0 0 20px rgba(0, 102, 255, 0.6),
      0 0 30px rgba(0, 102, 255, 0.4),
      0 0 40px rgba(0, 102, 255, 0.2);
  }
}

@keyframes aiTypewriter {
  0% {
    width: 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes aiTitleScan {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes aiSubtitleGlow {
  0%, 100% {
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
    color: #00ffff;
  }
  33% {
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
    color: #00ff88;
  }
  66% {
    text-shadow: 0 0 18px rgba(138, 43, 226, 0.7);
    color: #8a2be2;
  }
}

@keyframes aiTextStream {
  0% {
    opacity: 0;
    transform: translateX(-20px);
    filter: blur(3px);
  }
  25% {
    opacity: 0.3;
    transform: translateX(-10px);
    filter: blur(2px);
  }
  50% {
    opacity: 0.6;
    transform: translateX(-5px);
    filter: blur(1px);
  }
  75% {
    opacity: 0.8;
    transform: translateX(0px);
    filter: blur(0px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
    filter: blur(0px);
  }
}

@keyframes aiCursor {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* AI Hologram Effects */
@keyframes aiHologram {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(0, 102, 255, 0.6));
    text-shadow: 0 0 30px rgba(0, 102, 255, 0.4);
  }
  25% {
    filter: drop-shadow(0 0 25px rgba(138, 43, 226, 0.6));
    text-shadow: 0 0 35px rgba(138, 43, 226, 0.4);
  }
  50% {
    filter: drop-shadow(0 0 30px rgba(0, 255, 136, 0.6));
    text-shadow: 0 0 40px rgba(0, 255, 136, 0.4);
  }
  75% {
    filter: drop-shadow(0 0 25px rgba(0, 255, 255, 0.6));
    text-shadow: 0 0 35px rgba(0, 255, 255, 0.4);
  }
}

@keyframes aiGlitch {
  0%, 90%, 100% {
    opacity: 0.4;
    transform: translateX(0);
  }
  5% {
    opacity: 0.7;
    transform: translateX(2px);
  }
  10% {
    opacity: 0.3;
    transform: translateX(-2px);
  }
  15% {
    opacity: 0.6;
    transform: translateX(1px);
  }
  20% {
    opacity: 0.5;
    transform: translateX(-1px);
  }
}

@keyframes aiScanLine {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero::before {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
    padding: var(--space-4);
  }

  .hero-content {
    order: 2;
  }

  .hero-image {
    order: 1;
  }

  .avatar-placeholder {
    width: 240px;
    height: 240px;
    font-size: 6rem;
  }

  .hero-stats {
    gap: var(--space-6);
    margin: var(--space-6) 0;
  }

  .stat-number {
    font-size: var(--text-xl);
  }

  .hero-buttons {
    justify-content: center;
    gap: var(--space-3);
  }

  .hero-buttons .btn {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .hero-container {
    gap: var(--space-8);
  }

  .avatar-placeholder {
    width: 200px;
    height: 200px;
    font-size: 5rem;
  }

  .hero-stats {
    gap: var(--space-4);
    margin: var(--space-4) 0;
  }

  .stat-number {
    font-size: var(--text-lg);
  }

  .hero-buttons {
    flex-direction: column;
  }
}
