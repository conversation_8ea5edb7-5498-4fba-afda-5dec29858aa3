.skills {
  padding: var(--space-24) 0;
  background: var(--bg-secondary);
}

.skills-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
}

.skill-category {
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--duration-normal) var(--ease-out);
  opacity: 0;
  animation: slideInUp 0.8s var(--ease-out) forwards;
}

.skill-category:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--gray-300);
}

.category-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-align: center;
  position: relative;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-2);
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: var(--primary);
  border-radius: var(--radius-full);
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.skill-item {
  position: relative;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.skill-name {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.skill-percentage {
  font-weight: 600;
  color: var(--primary);
  font-size: var(--text-xs);
  font-family: 'JetBrains Mono', monospace;
}

.skill-bar {
  height: 6px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: var(--primary);
  border-radius: var(--radius-full);
  transition: width 1.5s var(--ease-out);
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Clean Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Stagger animation for skill categories */
.skill-category:nth-child(1) {
  animation-delay: 0s;
}

.skill-category:nth-child(2) {
  animation-delay: 0.2s;
}

.skill-category:nth-child(3) {
  animation-delay: 0.4s;
}

.skill-category:nth-child(4) {
  animation-delay: 0.6s;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills {
    padding: var(--space-20) 0;
  }

  .skills-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .skill-category {
    padding: var(--space-6);
  }

  .category-title {
    font-size: var(--text-lg);
  }

  .skill-name {
    font-size: var(--text-xs);
  }

  .skill-percentage {
    font-size: var(--text-xs);
  }
}
