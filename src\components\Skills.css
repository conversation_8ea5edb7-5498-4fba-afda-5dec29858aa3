.skills {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.skills-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
}

.skill-category {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  animation: fadeInUp 1s ease-out;
}

.skill-category:hover {
  transform: translateY(-5px);
}

.category-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skill-item {
  position: relative;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.skill-name {
  font-weight: 600;
  color: #4a5568;
  font-size: 1rem;
}

.skill-percentage {
  font-weight: 700;
  color: #667eea;
  font-size: 0.9rem;
}

.skill-bar {
  height: 8px;
  background: #e2e8f0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  transition: width 1.5s ease-in-out;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Stagger animation for skill categories */
.skill-category:nth-child(1) {
  animation-delay: 0s;
}

.skill-category:nth-child(2) {
  animation-delay: 0.2s;
}

.skill-category:nth-child(3) {
  animation-delay: 0.4s;
}

.skill-category:nth-child(4) {
  animation-delay: 0.6s;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .skills {
    padding: 3rem 0;
  }

  .skills-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skill-category {
    padding: 1.5rem;
  }

  .category-title {
    font-size: 1.3rem;
  }

  .skill-name {
    font-size: 0.9rem;
  }

  .skill-percentage {
    font-size: 0.8rem;
  }
}
