/* SKILLS SECTION - NEON CYBER MATRIX THEME */
.skills {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  border-top: 2px solid #00ffff;
  border-bottom: 2px solid #00ff00;
}

.skills::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(0, 255, 0, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 60% 20%, rgba(255, 0, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  animation: cyberPulse 8s ease-in-out infinite;
}

.skills::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.03) 100%),
    linear-gradient(0deg, transparent 98%, rgba(0, 255, 0, 0.03) 100%);
  background-size: 50px 50px;
  animation: matrixGrid 20s linear infinite;
  pointer-events: none;
}

/* Cyber Section Title */
.skills .section-title {
  color: #00ffff;
  background: linear-gradient(135deg, #00ffff 0%, #00ff00 50%, #ff00ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  animation: neonFlicker 3s ease-in-out infinite;
}

.skills .section-title::after {
  background: linear-gradient(90deg, #00ffff 0%, #00ff00 100%);
  height: 2px;
  width: 120px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.skills-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
}

.skill-category {
  background: rgba(10, 10, 10, 0.9);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  border: 2px solid #00ffff;
  transition: all var(--duration-normal) var(--ease-out);
  opacity: 0;
  animation: slideInUp 0.8s var(--ease-out) forwards;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
}

.skill-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-cosmic);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.skill-category::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-aurora);
  border-radius: var(--radius-xl);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.skill-category:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl), var(--glow-secondary);
  border-color: var(--secondary-light);
}

.skill-category:hover::before {
  opacity: 0.08;
}

.skill-category:hover::after {
  opacity: 0.3;
}

.category-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-align: center;
  position: relative;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-2);
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: var(--primary);
  border-radius: var(--radius-full);
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.skill-item {
  position: relative;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.skill-name {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.skill-percentage {
  font-weight: 600;
  color: var(--primary);
  font-size: var(--text-xs);
  font-family: 'JetBrains Mono', monospace;
}

.skill-bar {
  height: 8px;
  background: var(--gray-400);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.skill-progress {
  height: 100%;
  width: var(--skill-width, 0%);
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width 1.5s var(--ease-out);
  position: relative;
  box-shadow: var(--glow-primary);
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Clean Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Cyber Theme Animations */
@keyframes cyberPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes matrixGrid {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(50px);
  }
}

@keyframes neonFlicker {
  0%, 100% {
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 0, 0.3);
  }
}

/* Stagger animation for skill categories */
.skill-category:nth-child(1) {
  animation-delay: 0s;
}

.skill-category:nth-child(2) {
  animation-delay: 0.2s;
}

.skill-category:nth-child(3) {
  animation-delay: 0.4s;
}

.skill-category:nth-child(4) {
  animation-delay: 0.6s;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills {
    padding: var(--space-20) 0;
  }

  .skills-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .skill-category {
    padding: var(--space-6);
  }

  .category-title {
    font-size: var(--text-lg);
  }

  .skill-name {
    font-size: var(--text-xs);
  }

  .skill-percentage {
    font-size: var(--text-xs);
  }
}
