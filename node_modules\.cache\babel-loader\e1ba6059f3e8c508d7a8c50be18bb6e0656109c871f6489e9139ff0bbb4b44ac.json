{"ast": null, "code": "import React from'react';import'./About.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const About=()=>{return/*#__PURE__*/_jsx(\"section\",{id:\"about\",className:\"about\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"About Me\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"about-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"about-text\",children:[portfolioConfig.about.description.map((paragraph,index)=>/*#__PURE__*/_jsx(\"p\",{children:paragraph},index)),/*#__PURE__*/_jsx(\"div\",{className:\"about-stats\",children:portfolioConfig.about.stats.map((stat,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:stat.number}),/*#__PURE__*/_jsx(\"p\",{children:stat.label})]},index))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"about-image\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"image-placeholder\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCF8\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Your Photo Here\"})]})})]})]})});};export default About;", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "About", "id", "className", "children", "about", "description", "map", "paragraph", "index", "stats", "stat", "number", "label"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/About.tsx"], "sourcesContent": ["import React from 'react';\nimport './About.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\nconst About: React.FC = () => {\n  return (\n    <section id=\"about\" className=\"about\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">About Me</h2>\n        <div className=\"about-content\">\n          <div className=\"about-text\">\n            {portfolioConfig.about.description.map((paragraph, index) => (\n              <p key={index}>\n                {paragraph}\n              </p>\n            ))}\n            <div className=\"about-stats\">\n              {portfolioConfig.about.stats.map((stat, index) => (\n                <div key={index} className=\"stat\">\n                  <h3>{stat.number}</h3>\n                  <p>{stat.label}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div className=\"about-image\">\n            <div className=\"image-placeholder\">\n              <span>📸</span>\n              <p>Your Photo Here</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,aAAa,CACpB,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,mBACEH,IAAA,YAASI,EAAE,CAAC,OAAO,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cACnCJ,KAAA,QAAKG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBN,IAAA,OAAIK,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC3CJ,KAAA,QAAKG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BJ,KAAA,QAAKG,SAAS,CAAC,YAAY,CAAAC,QAAA,EACxBR,eAAe,CAACS,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC,CAACC,SAAS,CAAEC,KAAK,gBACtDX,IAAA,MAAAM,QAAA,CACGI,SAAS,EADJC,KAEL,CACJ,CAAC,cACFX,IAAA,QAAKK,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBR,eAAe,CAACS,KAAK,CAACK,KAAK,CAACH,GAAG,CAAC,CAACI,IAAI,CAAEF,KAAK,gBAC3CT,KAAA,QAAiBG,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC/BN,IAAA,OAAAM,QAAA,CAAKO,IAAI,CAACC,MAAM,CAAK,CAAC,cACtBd,IAAA,MAAAM,QAAA,CAAIO,IAAI,CAACE,KAAK,CAAI,CAAC,GAFXJ,KAGL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cACNX,IAAA,QAAKK,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BJ,KAAA,QAAKG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCN,IAAA,SAAAM,QAAA,CAAM,cAAE,CAAM,CAAC,cACfN,IAAA,MAAAM,QAAA,CAAG,iBAAe,CAAG,CAAC,EACnB,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}