import React, { useState } from 'react';
import './Certificates.css';
import { portfolioConfig } from '../config/portfolioConfig';

interface Certificate {
  id: number;
  name: string;
  issuer: string;
  date: string;
  credentialId?: string;
  verificationUrl?: string;
  description: string;
  skills: string[];
  category: string;
  image: string;
}

const Certificates: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('All');
  const certificates: Certificate[] = portfolioConfig.certificates || [];
  
  const categories = ['All', ...Array.from(new Set(certificates.map(cert => cert.category)))];
  
  const filteredCertificates = activeFilter === 'All' 
    ? certificates 
    : certificates.filter(cert => cert.category === activeFilter);

  return (
    <section id="certificates" className="certificates">
      <div className="container">
        <h2 className="section-title">Certifications & Achievements</h2>
        
        <div className="certificate-filters">
          {categories.map((category) => (
            <button
              key={category}
              type="button"
              className={`filter-btn ${activeFilter === category ? 'active' : ''}`}
              onClick={() => setActiveFilter(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="certificates-grid">
          {filteredCertificates.map((cert) => (
            <div key={cert.id} className="certificate-card">
              <div className="certificate-header">
                <div className="certificate-image">
                  <span className="cert-icon">{cert.image}</span>
                </div>
                <div className="certificate-badge">
                  <span className="category-badge">{cert.category}</span>
                </div>
              </div>
              
              <div className="certificate-content">
                <h3 className="certificate-name">{cert.name}</h3>
                <h4 className="certificate-issuer">{cert.issuer}</h4>
                <p className="certificate-date">Issued: {cert.date}</p>
                
                <p className="certificate-description">{cert.description}</p>
                
                <div className="certificate-skills">
                  <h5>Skills Covered:</h5>
                  <div className="skills-tags">
                    {cert.skills.map((skill, idx) => (
                      <span key={idx} className="skill-tag">{skill}</span>
                    ))}
                  </div>
                </div>

                <div className="certificate-actions">
                  {cert.credentialId && (
                    <div className="credential-id">
                      <strong>ID:</strong> {cert.credentialId}
                    </div>
                  )}
                  {cert.verificationUrl && (
                    <a 
                      href={cert.verificationUrl} 
                      className="verify-btn"
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      Verify Certificate
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredCertificates.length === 0 && (
          <div className="no-certificates">
            <p>No certificates found for the selected category.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default Certificates;
