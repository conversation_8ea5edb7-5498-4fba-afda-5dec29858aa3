import React, { useState, useEffect } from 'react';
import './AccessibilityHelper.css';

const AccessibilityHelper: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [highContrast, setHighContrast] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);

  useEffect(() => {
    // Apply font size
    document.documentElement.style.fontSize = `${fontSize}px`;
  }, [fontSize]);

  useEffect(() => {
    // Apply high contrast
    if (highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
  }, [highContrast]);

  useEffect(() => {
    // Apply reduced motion
    if (reducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }
  }, [reducedMotion]);

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 24));
  };

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 12));
  };

  const resetFontSize = () => {
    setFontSize(16);
  };

  const toggleHighContrast = () => {
    setHighContrast(prev => !prev);
  };

  const toggleReducedMotion = () => {
    setReducedMotion(prev => !prev);
  };

  return (
    <>
      <button
        className="accessibility-toggle"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Open accessibility options"
        aria-expanded={isOpen}
        type="button"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 5.5C14.8 5.5 14.6 5.4 14.5 5.3L13 3.8C12.6 3.4 12.1 3.2 11.5 3.2S10.4 3.4 10 3.8L8.5 5.3C8.4 5.4 8.2 5.5 8 5.5L2 7V9L8 7.5V10.5C8 11.1 8.4 11.6 9 11.8L11 12.5V19C11 19.6 11.4 20 12 20S13 19.6 13 19V12.5L15 11.8C15.6 11.6 16 11.1 16 10.5V7.5L21 9Z"
            fill="currentColor"
          />
        </svg>
      </button>

      <div className={`accessibility-panel ${isOpen ? 'open' : ''}`}>
        <div className="accessibility-header">
          <h3>Accessibility Options</h3>
          <button
            className="close-btn"
            onClick={() => setIsOpen(false)}
            aria-label="Close accessibility options"
            type="button"
          >
            ×
          </button>
        </div>

        <div className="accessibility-content">
          <div className="accessibility-group">
            <h4>Font Size</h4>
            <div className="font-controls">
              <button
                onClick={decreaseFontSize}
                aria-label="Decrease font size"
                disabled={fontSize <= 12}
                type="button"
              >
                A-
              </button>
              <span className="font-size-display">{fontSize}px</span>
              <button
                onClick={increaseFontSize}
                aria-label="Increase font size"
                disabled={fontSize >= 24}
                type="button"
              >
                A+
              </button>
              <button
                onClick={resetFontSize}
                aria-label="Reset font size"
                type="button"
              >
                Reset
              </button>
            </div>
          </div>

          <div className="accessibility-group">
            <h4>Visual Options</h4>
            <label className="accessibility-checkbox">
              <input
                type="checkbox"
                checked={highContrast}
                onChange={toggleHighContrast}
                aria-describedby="contrast-desc"
              />
              <span className="checkmark"></span>
              High Contrast Mode
            </label>
            <p id="contrast-desc" className="option-description">
              Increases contrast for better visibility
            </p>
          </div>

          <div className="accessibility-group">
            <h4>Motion Options</h4>
            <label className="accessibility-checkbox">
              <input
                type="checkbox"
                checked={reducedMotion}
                onChange={toggleReducedMotion}
                aria-describedby="motion-desc"
              />
              <span className="checkmark"></span>
              Reduce Motion
            </label>
            <p id="motion-desc" className="option-description">
              Reduces animations and transitions
            </p>
          </div>
        </div>
      </div>

      {isOpen && (
        <div
          className="accessibility-overlay"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        ></div>
      )}
    </>
  );
};

export default AccessibilityHelper;
