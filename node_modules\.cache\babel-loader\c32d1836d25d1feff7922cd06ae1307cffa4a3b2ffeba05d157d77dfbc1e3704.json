{"ast": null, "code": "import React,{useState}from'react';import'./Contact.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Contact=()=>{const[formData,setFormData]=useState({name:'',email:'',subject:'',message:''});const[isSubmitting,setIsSubmitting]=useState(false);const[submitMessage,setSubmitMessage]=useState('');const handleChange=e=>{const{name,value}=e.target;setFormData(prev=>({...prev,[name]:value}));};const handleSubmit=async e=>{e.preventDefault();setIsSubmitting(true);// Simulate form submission\nsetTimeout(()=>{setSubmitMessage('Thank you for your message! I\\'ll get back to you soon.');setFormData({name:'',email:'',subject:'',message:''});setIsSubmitting(false);},1000);};return/*#__PURE__*/_jsx(\"section\",{id:\"contact\",className:\"contact\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title\",children:\"Get In Touch\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"contact-info\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Let's Work Together\"}),/*#__PURE__*/_jsx(\"p\",{children:\"I'm always interested in new opportunities and exciting projects. Whether you have a question or just want to say hi, feel free to reach out!\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"contact-icon\",children:\"\\uD83D\\uDCE7\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Email\"}),/*#__PURE__*/_jsx(\"p\",{children:portfolioConfig.personal.email})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"contact-icon\",children:\"\\uD83D\\uDCF1\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Phone\"}),/*#__PURE__*/_jsx(\"p\",{children:portfolioConfig.personal.phone})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"contact-icon\",children:\"\\uD83D\\uDCCD\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Location\"}),/*#__PURE__*/_jsx(\"p\",{children:portfolioConfig.personal.location})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"social-links\",children:[/*#__PURE__*/_jsx(\"a\",{href:portfolioConfig.social.linkedin,className:\"social-link\",\"aria-label\":\"LinkedIn\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"\\uD83D\\uDCBC\"}),/*#__PURE__*/_jsx(\"a\",{href:portfolioConfig.social.github,className:\"social-link\",\"aria-label\":\"GitHub\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"\\uD83D\\uDC19\"}),/*#__PURE__*/_jsx(\"a\",{href:portfolioConfig.social.twitter,className:\"social-link\",\"aria-label\":\"Twitter\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"\\uD83D\\uDC26\"}),/*#__PURE__*/_jsx(\"a\",{href:portfolioConfig.social.instagram,className:\"social-link\",\"aria-label\":\"Instagram\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"\\uD83D\\uDCF7\"})]})]}),/*#__PURE__*/_jsxs(\"form\",{className:\"contact-form\",onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"name\",placeholder:\"Your Name\",value:formData.name,onChange:handleChange,required:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",placeholder:\"Your Email\",value:formData.email,onChange:handleChange,required:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"subject\",placeholder:\"Subject\",value:formData.subject,onChange:handleChange,required:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:/*#__PURE__*/_jsx(\"textarea\",{name:\"message\",placeholder:\"Your Message\",rows:5,value:formData.message,onChange:handleChange,required:true})}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-btn\",disabled:isSubmitting,children:isSubmitting?'Sending...':'Send Message'}),submitMessage&&/*#__PURE__*/_jsx(\"p\",{className:\"submit-message\",children:submitMessage})]})]})]})});};export default Contact;", "map": {"version": 3, "names": ["React", "useState", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Contact", "formData", "setFormData", "name", "email", "subject", "message", "isSubmitting", "setIsSubmitting", "submitMessage", "setSubmitMessage", "handleChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "setTimeout", "id", "className", "children", "personal", "phone", "location", "href", "social", "linkedin", "rel", "github", "twitter", "instagram", "onSubmit", "type", "placeholder", "onChange", "required", "rows", "disabled"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Contact.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Contact.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface FormData {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\nconst Contact: React.FC = () => {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    setTimeout(() => {\n      setSubmitMessage('Thank you for your message! I\\'ll get back to you soon.');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n      setIsSubmitting(false);\n    }, 1000);\n  };\n\n  return (\n    <section id=\"contact\" className=\"contact\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Get In Touch</h2>\n        <div className=\"contact-content\">\n          <div className=\"contact-info\">\n            <h3>Let's Work Together</h3>\n            <p>\n              I'm always interested in new opportunities and exciting projects. \n              Whether you have a question or just want to say hi, feel free to reach out!\n            </p>\n            \n            <div className=\"contact-details\">\n              <div className=\"contact-item\">\n                <span className=\"contact-icon\">📧</span>\n                <div>\n                  <h4>Email</h4>\n                  <p>{portfolioConfig.personal.email}</p>\n                </div>\n              </div>\n              <div className=\"contact-item\">\n                <span className=\"contact-icon\">📱</span>\n                <div>\n                  <h4>Phone</h4>\n                  <p>{portfolioConfig.personal.phone}</p>\n                </div>\n              </div>\n              <div className=\"contact-item\">\n                <span className=\"contact-icon\">📍</span>\n                <div>\n                  <h4>Location</h4>\n                  <p>{portfolioConfig.personal.location}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"social-links\">\n              <a href={portfolioConfig.social.linkedin} className=\"social-link\" aria-label=\"LinkedIn\" target=\"_blank\" rel=\"noopener noreferrer\">💼</a>\n              <a href={portfolioConfig.social.github} className=\"social-link\" aria-label=\"GitHub\" target=\"_blank\" rel=\"noopener noreferrer\">🐙</a>\n              <a href={portfolioConfig.social.twitter} className=\"social-link\" aria-label=\"Twitter\" target=\"_blank\" rel=\"noopener noreferrer\">🐦</a>\n              <a href={portfolioConfig.social.instagram} className=\"social-link\" aria-label=\"Instagram\" target=\"_blank\" rel=\"noopener noreferrer\">📷</a>\n            </div>\n          </div>\n\n          <form className=\"contact-form\" onSubmit={handleSubmit}>\n            <div className=\"form-group\">\n              <input\n                type=\"text\"\n                name=\"name\"\n                placeholder=\"Your Name\"\n                value={formData.name}\n                onChange={handleChange}\n                required\n              />\n            </div>\n            <div className=\"form-group\">\n              <input\n                type=\"email\"\n                name=\"email\"\n                placeholder=\"Your Email\"\n                value={formData.email}\n                onChange={handleChange}\n                required\n              />\n            </div>\n            <div className=\"form-group\">\n              <input\n                type=\"text\"\n                name=\"subject\"\n                placeholder=\"Subject\"\n                value={formData.subject}\n                onChange={handleChange}\n                required\n              />\n            </div>\n            <div className=\"form-group\">\n              <textarea\n                name=\"message\"\n                placeholder=\"Your Message\"\n                rows={5}\n                value={formData.message}\n                onChange={handleChange}\n                required\n              ></textarea>\n            </div>\n            <button \n              type=\"submit\" \n              className=\"submit-btn\"\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? 'Sending...' : 'Send Message'}\n            </button>\n            {submitMessage && (\n              <p className=\"submit-message\">{submitMessage}</p>\n            )}\n          </form>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,eAAe,CACtB,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS5D,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGR,QAAQ,CAAW,CACjDS,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACe,aAAa,CAAEC,gBAAgB,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAAiB,YAAY,CAAIC,CAA4D,EAAK,CACrF,KAAM,CAAET,IAAI,CAAEU,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCZ,WAAW,CAACa,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACZ,IAAI,EAAGU,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAJ,CAAkB,EAAK,CACjDA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBT,eAAe,CAAC,IAAI,CAAC,CAErB;AACAU,UAAU,CAAC,IAAM,CACfR,gBAAgB,CAAC,yDAAyD,CAAC,CAC3ER,WAAW,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,mBACEX,IAAA,YAASsB,EAAE,CAAC,SAAS,CAACC,SAAS,CAAC,SAAS,CAAAC,QAAA,cACvCtB,KAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxB,IAAA,OAAIuB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC/CtB,KAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtB,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxB,IAAA,OAAAwB,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BxB,IAAA,MAAAwB,QAAA,CAAG,+IAGH,CAAG,CAAC,cAEJtB,KAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtB,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxB,IAAA,SAAMuB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACxCtB,KAAA,QAAAsB,QAAA,eACExB,IAAA,OAAAwB,QAAA,CAAI,OAAK,CAAI,CAAC,cACdxB,IAAA,MAAAwB,QAAA,CAAI1B,eAAe,CAAC2B,QAAQ,CAAClB,KAAK,CAAI,CAAC,EACpC,CAAC,EACH,CAAC,cACNL,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxB,IAAA,SAAMuB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACxCtB,KAAA,QAAAsB,QAAA,eACExB,IAAA,OAAAwB,QAAA,CAAI,OAAK,CAAI,CAAC,cACdxB,IAAA,MAAAwB,QAAA,CAAI1B,eAAe,CAAC2B,QAAQ,CAACC,KAAK,CAAI,CAAC,EACpC,CAAC,EACH,CAAC,cACNxB,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxB,IAAA,SAAMuB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACxCtB,KAAA,QAAAsB,QAAA,eACExB,IAAA,OAAAwB,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjBxB,IAAA,MAAAwB,QAAA,CAAI1B,eAAe,CAAC2B,QAAQ,CAACE,QAAQ,CAAI,CAAC,EACvC,CAAC,EACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKqB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxB,IAAA,MAAG4B,IAAI,CAAE9B,eAAe,CAAC+B,MAAM,CAACC,QAAS,CAACP,SAAS,CAAC,aAAa,CAAC,aAAW,UAAU,CAACN,MAAM,CAAC,QAAQ,CAACc,GAAG,CAAC,qBAAqB,CAAAP,QAAA,CAAC,cAAE,CAAG,CAAC,cACxIxB,IAAA,MAAG4B,IAAI,CAAE9B,eAAe,CAAC+B,MAAM,CAACG,MAAO,CAACT,SAAS,CAAC,aAAa,CAAC,aAAW,QAAQ,CAACN,MAAM,CAAC,QAAQ,CAACc,GAAG,CAAC,qBAAqB,CAAAP,QAAA,CAAC,cAAE,CAAG,CAAC,cACpIxB,IAAA,MAAG4B,IAAI,CAAE9B,eAAe,CAAC+B,MAAM,CAACI,OAAQ,CAACV,SAAS,CAAC,aAAa,CAAC,aAAW,SAAS,CAACN,MAAM,CAAC,QAAQ,CAACc,GAAG,CAAC,qBAAqB,CAAAP,QAAA,CAAC,cAAE,CAAG,CAAC,cACtIxB,IAAA,MAAG4B,IAAI,CAAE9B,eAAe,CAAC+B,MAAM,CAACK,SAAU,CAACX,SAAS,CAAC,aAAa,CAAC,aAAW,WAAW,CAACN,MAAM,CAAC,QAAQ,CAACc,GAAG,CAAC,qBAAqB,CAAAP,QAAA,CAAC,cAAE,CAAG,CAAC,EACvI,CAAC,EACH,CAAC,cAENtB,KAAA,SAAMqB,SAAS,CAAC,cAAc,CAACY,QAAQ,CAAEhB,YAAa,CAAAK,QAAA,eACpDxB,IAAA,QAAKuB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBxB,IAAA,UACEoC,IAAI,CAAC,MAAM,CACX9B,IAAI,CAAC,MAAM,CACX+B,WAAW,CAAC,WAAW,CACvBrB,KAAK,CAAEZ,QAAQ,CAACE,IAAK,CACrBgC,QAAQ,CAAExB,YAAa,CACvByB,QAAQ,MACT,CAAC,CACC,CAAC,cACNvC,IAAA,QAAKuB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBxB,IAAA,UACEoC,IAAI,CAAC,OAAO,CACZ9B,IAAI,CAAC,OAAO,CACZ+B,WAAW,CAAC,YAAY,CACxBrB,KAAK,CAAEZ,QAAQ,CAACG,KAAM,CACtB+B,QAAQ,CAAExB,YAAa,CACvByB,QAAQ,MACT,CAAC,CACC,CAAC,cACNvC,IAAA,QAAKuB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBxB,IAAA,UACEoC,IAAI,CAAC,MAAM,CACX9B,IAAI,CAAC,SAAS,CACd+B,WAAW,CAAC,SAAS,CACrBrB,KAAK,CAAEZ,QAAQ,CAACI,OAAQ,CACxB8B,QAAQ,CAAExB,YAAa,CACvByB,QAAQ,MACT,CAAC,CACC,CAAC,cACNvC,IAAA,QAAKuB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBxB,IAAA,aACEM,IAAI,CAAC,SAAS,CACd+B,WAAW,CAAC,cAAc,CAC1BG,IAAI,CAAE,CAAE,CACRxB,KAAK,CAAEZ,QAAQ,CAACK,OAAQ,CACxB6B,QAAQ,CAAExB,YAAa,CACvByB,QAAQ,MACC,CAAC,CACT,CAAC,cACNvC,IAAA,WACEoC,IAAI,CAAC,QAAQ,CACbb,SAAS,CAAC,YAAY,CACtBkB,QAAQ,CAAE/B,YAAa,CAAAc,QAAA,CAEtBd,YAAY,CAAG,YAAY,CAAG,cAAc,CACvC,CAAC,CACRE,aAAa,eACZZ,IAAA,MAAGuB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEZ,aAAa,CAAI,CACjD,EACG,CAAC,EACJ,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAT,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}