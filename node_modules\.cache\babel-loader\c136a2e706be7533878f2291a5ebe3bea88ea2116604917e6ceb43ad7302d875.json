{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\About.tsx\";\nimport React from 'react';\nimport './About.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"about\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"About Me\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about-text\",\n          children: [portfolioConfig.about.description.map((paragraph, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n            children: paragraph\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"about-stats\",\n            children: portfolioConfig.about.stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: stat.number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about-image\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-placeholder\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83D\\uDCF8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Your Photo Here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsxDEV", "_jsxDEV", "About", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "about", "description", "map", "paragraph", "index", "stats", "stat", "number", "label", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/About.tsx"], "sourcesContent": ["import React from 'react';\nimport './About.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\nconst About: React.FC = () => {\n  return (\n    <section id=\"about\" className=\"about\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">About Me</h2>\n        <div className=\"about-content\">\n          <div className=\"about-text\">\n            {portfolioConfig.about.description.map((paragraph, index) => (\n              <p key={index}>\n                {paragraph}\n              </p>\n            ))}\n            <div className=\"about-stats\">\n              {portfolioConfig.about.stats.map((stat, index) => (\n                <div key={index} className=\"stat\">\n                  <h3>{stat.number}</h3>\n                  <p>{stat.label}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div className=\"about-image\">\n            <div className=\"image-placeholder\">\n              <span>📸</span>\n              <p>Your Photo Here</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAC5B,oBACED,OAAA;IAASE,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,OAAO;IAAAC,QAAA,eACnCJ,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBJ,OAAA;QAAIG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CR,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BJ,OAAA;UAAKG,SAAS,EAAC,YAAY;UAAAC,QAAA,GACxBN,eAAe,CAACW,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBACtDb,OAAA;YAAAI,QAAA,EACGQ;UAAS,GADJC,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACJ,CAAC,eACFR,OAAA;YAAKG,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBN,eAAe,CAACW,KAAK,CAACK,KAAK,CAACH,GAAG,CAAC,CAACI,IAAI,EAAEF,KAAK,kBAC3Cb,OAAA;cAAiBG,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAAI,QAAA,EAAKW,IAAI,CAACC;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtBR,OAAA;gBAAAI,QAAA,EAAIW,IAAI,CAACE;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAFXK,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNR,OAAA;UAAKG,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BJ,OAAA;YAAKG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCJ,OAAA;cAAAI,QAAA,EAAM;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfR,OAAA;cAAAI,QAAA,EAAG;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACU,EAAA,GA/BIjB,KAAe;AAiCrB,eAAeA,KAAK;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}