{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './App.css';\nimport LoadingScreen from './components/LoadingScreen';\nimport ScrollProgress from './components/ScrollProgress';\nimport CursorFollower from './components/CursorFollower';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Experience from './components/Experience';\nimport Certificates from './components/Certificates';\nimport Projects from './components/Projects';\nimport Contact from './components/Contact';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isLoading, setIsLoading] = useState(true);\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [isLoading && /*#__PURE__*/_jsxDEV(LoadingScreen, {\n      onLoadingComplete: handleLoadingComplete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 21\n    }, this), !isLoading && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(CursorFollower, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ScrollProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Experience, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Certificates, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"5S7VQ8+9ArWv2AFPIfnY+LwrHeg=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "LoadingScreen", "ScrollProgress", "Cursor<PERSON><PERSON>ow<PERSON>", "Header", "Hero", "About", "Skills", "Experience", "Certificates", "Projects", "Contact", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isLoading", "setIsLoading", "handleLoadingComplete", "className", "children", "onLoadingComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './App.css';\nimport LoadingScreen from './components/LoadingScreen';\nimport ScrollProgress from './components/ScrollProgress';\nimport CursorFollower from './components/CursorFollower';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Experience from './components/Experience';\nimport Certificates from './components/Certificates';\nimport Projects from './components/Projects';\nimport Contact from './components/Contact';\n\nfunction App() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n\n  return (\n    <div className=\"App\">\n      {isLoading && <LoadingScreen onLoadingComplete={handleLoadingComplete} />}\n\n      {!isLoading && (\n        <>\n          <CursorFollower />\n          <ScrollProgress />\n          <Header />\n          <main>\n            <Hero />\n            <About />\n            <Skills />\n            <Experience />\n            <Certificates />\n            <Projects />\n            <Contact />\n          </main>\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,WAAW;AAClB,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMoB,qBAAqB,GAAGA,CAAA,KAAM;IAClCD,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEN,OAAA;IAAKQ,SAAS,EAAC,KAAK;IAAAC,QAAA,GACjBJ,SAAS,iBAAIL,OAAA,CAACZ,aAAa;MAACsB,iBAAiB,EAAEH;IAAsB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAExE,CAACT,SAAS,iBACTL,OAAA,CAAAE,SAAA;MAAAO,QAAA,gBACET,OAAA,CAACV,cAAc;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBd,OAAA,CAACX,cAAc;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBd,OAAA,CAACT,MAAM;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVd,OAAA;QAAAS,QAAA,gBACET,OAAA,CAACR,IAAI;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACRd,OAAA,CAACP,KAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACTd,OAAA,CAACN,MAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVd,OAAA,CAACL,UAAU;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdd,OAAA,CAACJ,YAAY;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChBd,OAAA,CAACH,QAAQ;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACZd,OAAA,CAACF,OAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA,eACP,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACV,EAAA,CA7BQD,GAAG;AAAAY,EAAA,GAAHZ,GAAG;AA+BZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}