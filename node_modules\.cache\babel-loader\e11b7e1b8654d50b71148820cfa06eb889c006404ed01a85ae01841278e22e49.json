{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Projects.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  _s();\n  const [activeFilter, setActiveFilter] = useState('All');\n  const projects = portfolioConfig.projects;\n  const categories = ['All', 'Web App', 'Frontend', 'Mobile'];\n  const filteredProjects = activeFilter === 'All' ? projects : projects.filter(project => project.category === activeFilter);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"projects\",\n    className: \"projects\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Featured Projects\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"project-filters\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: `filter-btn ${activeFilter === category ? 'active' : ''}`,\n          onClick: () => setActiveFilter(category),\n          children: category\n        }, category, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"projects-grid\",\n        children: filteredProjects.map(project => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"project-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-image\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"project-emoji\",\n              children: project.image\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"project-title\",\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"project-description\",\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-technologies\",\n              children: project.technologies.map(tech => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tech-tag\",\n                children: tech\n              }, tech, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-links\",\n              children: [project.liveUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                href: project.liveUrl,\n                className: \"project-link\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"Live Demo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this), project.githubUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                href: project.githubUrl,\n                className: \"project-link\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"GitHub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this)]\n        }, project.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Projects, \"ozHZ0P52e9OmiFFgr1T32bValso=\");\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "portfolioConfig", "jsxDEV", "_jsxDEV", "Projects", "_s", "activeFilter", "setActiveFilter", "projects", "categories", "filteredProjects", "filter", "project", "category", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "type", "onClick", "image", "title", "description", "technologies", "tech", "liveUrl", "href", "target", "rel", "githubUrl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Projects.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Projects.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\ninterface Project {\n  id: number;\n  title: string;\n  description: string;\n  technologies: string[];\n  image: string;\n  liveUrl?: string;\n  githubUrl?: string;\n  category: string;\n}\n\nconst Projects: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('All');\n\n  const projects: Project[] = portfolioConfig.projects;\n\n  const categories = ['All', 'Web App', 'Frontend', 'Mobile'];\n\n  const filteredProjects = activeFilter === 'All' \n    ? projects \n    : projects.filter(project => project.category === activeFilter);\n\n  return (\n    <section id=\"projects\" className=\"projects\">\n      <div className=\"container\">\n        <h2 className=\"section-title\">Featured Projects</h2>\n        \n        <div className=\"project-filters\">\n          {categories.map((category) => (\n            <button\n              key={category}\n              type=\"button\"\n              className={`filter-btn ${activeFilter === category ? 'active' : ''}`}\n              onClick={() => setActiveFilter(category)}\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"projects-grid\">\n          {filteredProjects.map((project) => (\n            <div key={project.id} className=\"project-card\">\n              <div className=\"project-image\">\n                <span className=\"project-emoji\">{project.image}</span>\n              </div>\n              <div className=\"project-content\">\n                <h3 className=\"project-title\">{project.title}</h3>\n                <p className=\"project-description\">{project.description}</p>\n                <div className=\"project-technologies\">\n                  {project.technologies.map((tech) => (\n                    <span key={tech} className=\"tech-tag\">{tech}</span>\n                  ))}\n                </div>\n                <div className=\"project-links\">\n                  {project.liveUrl && (\n                    <a href={project.liveUrl} className=\"project-link\" target=\"_blank\" rel=\"noopener noreferrer\">\n                      Live Demo\n                    </a>\n                  )}\n                  {project.githubUrl && (\n                    <a href={project.githubUrl} className=\"project-link\" target=\"_blank\" rel=\"noopener noreferrer\">\n                      GitHub\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,gBAAgB;AACvB,SAASC,eAAe,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa5D,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMQ,QAAmB,GAAGP,eAAe,CAACO,QAAQ;EAEpD,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;EAE3D,MAAMC,gBAAgB,GAAGJ,YAAY,KAAK,KAAK,GAC3CE,QAAQ,GACRA,QAAQ,CAACG,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACC,QAAQ,KAAKP,YAAY,CAAC;EAEjE,oBACEH,OAAA;IAASW,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,UAAU;IAAAC,QAAA,eACzCb,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBb,OAAA;QAAIY,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEpDjB,OAAA;QAAKY,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BP,UAAU,CAACY,GAAG,CAAER,QAAQ,iBACvBV,OAAA;UAEEmB,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAE,cAAcT,YAAY,KAAKO,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrEU,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAACM,QAAQ,CAAE;UAAAG,QAAA,EAExCH;QAAQ,GALJA,QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjB,OAAA;QAAKY,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BN,gBAAgB,CAACW,GAAG,CAAET,OAAO,iBAC5BT,OAAA;UAAsBY,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC5Cb,OAAA;YAAKY,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5Bb,OAAA;cAAMY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEJ,OAAO,CAACY;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNjB,OAAA;YAAKY,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9Bb,OAAA;cAAIY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEJ,OAAO,CAACa;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClDjB,OAAA;cAAGY,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEJ,OAAO,CAACc;YAAW;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DjB,OAAA;cAAKY,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCJ,OAAO,CAACe,YAAY,CAACN,GAAG,CAAEO,IAAI,iBAC7BzB,OAAA;gBAAiBY,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEY;cAAI,GAAhCA,IAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmC,CACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjB,OAAA;cAAKY,SAAS,EAAC,eAAe;cAAAC,QAAA,GAC3BJ,OAAO,CAACiB,OAAO,iBACd1B,OAAA;gBAAG2B,IAAI,EAAElB,OAAO,CAACiB,OAAQ;gBAACd,SAAS,EAAC,cAAc;gBAACgB,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAAAhB,QAAA,EAAC;cAE7F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ,EACAR,OAAO,CAACqB,SAAS,iBAChB9B,OAAA;gBAAG2B,IAAI,EAAElB,OAAO,CAACqB,SAAU;gBAAClB,SAAS,EAAC,cAAc;gBAACgB,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAAAhB,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAxBER,OAAO,CAACE,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACf,EAAA,CA9DID,QAAkB;AAAA8B,EAAA,GAAlB9B,QAAkB;AAgExB,eAAeA,QAAQ;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}