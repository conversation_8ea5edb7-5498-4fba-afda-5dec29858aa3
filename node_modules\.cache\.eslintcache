[{"C:\\Users\\<USER>\\Desktop\\portfolio\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Skills.tsx": "4", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\About.tsx": "5", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Header.tsx": "6", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Projects.tsx": "7", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Hero.tsx": "8", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Contact.tsx": "9", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\config\\portfolioConfig.ts": "10", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Certificates.tsx": "11", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Experience.tsx": "12"}, {"size": 554, "mtime": 1752331176844, "results": "13", "hashOfConfig": "14"}, {"size": 425, "mtime": 1752331173188, "results": "15", "hashOfConfig": "14"}, {"size": 684, "mtime": 1752332748791, "results": "16", "hashOfConfig": "14"}, {"size": 1604, "mtime": 1752331632352, "results": "17", "hashOfConfig": "14"}, {"size": 1112, "mtime": 1752331617642, "results": "18", "hashOfConfig": "14"}, {"size": 1682, "mtime": 1752331733704, "results": "19", "hashOfConfig": "14"}, {"size": 2591, "mtime": 1752331657310, "results": "20", "hashOfConfig": "14"}, {"size": 1788, "mtime": 1752331596141, "results": "21", "hashOfConfig": "14"}, {"size": 4812, "mtime": 1752331689618, "results": "22", "hashOfConfig": "14"}, {"size": 10626, "mtime": 1752332951609, "results": "23", "hashOfConfig": "14"}, {"size": 3568, "mtime": 1752332639240, "results": "24", "hashOfConfig": "14"}, {"size": 2323, "mtime": 1752332588345, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16qp0og", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Skills.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\config\\portfolioConfig.ts", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Certificates.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Experience.tsx", [], []]