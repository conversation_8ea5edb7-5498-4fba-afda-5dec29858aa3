[{"C:\\Users\\<USER>\\Desktop\\portfolio\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Skills.tsx": "4", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\About.tsx": "5", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Header.tsx": "6", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Projects.tsx": "7", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Hero.tsx": "8", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Contact.tsx": "9", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\config\\portfolioConfig.ts": "10"}, {"size": 554, "mtime": 1752331176844, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1752331173188, "results": "13", "hashOfConfig": "12"}, {"size": 532, "mtime": 1752331293346, "results": "14", "hashOfConfig": "12"}, {"size": 1604, "mtime": 1752331632352, "results": "15", "hashOfConfig": "12"}, {"size": 1112, "mtime": 1752331617642, "results": "16", "hashOfConfig": "12"}, {"size": 1682, "mtime": 1752331733704, "results": "17", "hashOfConfig": "12"}, {"size": 2591, "mtime": 1752331657310, "results": "18", "hashOfConfig": "12"}, {"size": 1788, "mtime": 1752331596141, "results": "19", "hashOfConfig": "12"}, {"size": 4812, "mtime": 1752331689618, "results": "20", "hashOfConfig": "12"}, {"size": 4316, "mtime": 1752332483060, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16qp0og", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Skills.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\config\\portfolioConfig.ts", [], []]