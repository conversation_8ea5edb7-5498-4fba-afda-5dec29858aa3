[{"C:\\Users\\<USER>\\Desktop\\portfolio\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Skills.tsx": "4", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\About.tsx": "5", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Header.tsx": "6", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Projects.tsx": "7", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Hero.tsx": "8", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Contact.tsx": "9", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\config\\portfolioConfig.ts": "10", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Certificates.tsx": "11", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Experience.tsx": "12", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\LoadingScreen.tsx": "13", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\ScrollProgress.tsx": "14", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\CursorFollower.tsx": "15", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\DarkModeToggle.tsx": "16", "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\AccessibilityHelper.tsx": "17"}, {"size": 554, "mtime": 1752331176844, "results": "18", "hashOfConfig": "19"}, {"size": 425, "mtime": 1752331173188, "results": "20", "hashOfConfig": "19"}, {"size": 1331, "mtime": 1752333756049, "results": "21", "hashOfConfig": "19"}, {"size": 1711, "mtime": 1752335412174, "results": "22", "hashOfConfig": "19"}, {"size": 1112, "mtime": 1752331617642, "results": "23", "hashOfConfig": "19"}, {"size": 1872, "mtime": 1752334536287, "results": "24", "hashOfConfig": "19"}, {"size": 2591, "mtime": 1752331657310, "results": "25", "hashOfConfig": "19"}, {"size": 1788, "mtime": 1752331596141, "results": "26", "hashOfConfig": "19"}, {"size": 4812, "mtime": 1752331689618, "results": "27", "hashOfConfig": "19"}, {"size": 12178, "mtime": 1752333839816, "results": "28", "hashOfConfig": "19"}, {"size": 3568, "mtime": 1752332639240, "results": "29", "hashOfConfig": "19"}, {"size": 2323, "mtime": 1752332588345, "results": "30", "hashOfConfig": "19"}, {"size": 1821, "mtime": 1752335393808, "results": "31", "hashOfConfig": "19"}, {"size": 2645, "mtime": 1752335402793, "results": "32", "hashOfConfig": "19"}, {"size": 2335, "mtime": 1752335381247, "results": "33", "hashOfConfig": "19"}, {"size": 2836, "mtime": 1752333652623, "results": "34", "hashOfConfig": "19"}, {"size": 4885, "mtime": 1752335355331, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16qp0og", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Skills.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\config\\portfolioConfig.ts", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Certificates.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\Experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\LoadingScreen.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\ScrollProgress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\CursorFollower.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\portfolio\\src\\components\\AccessibilityHelper.tsx", [], []]