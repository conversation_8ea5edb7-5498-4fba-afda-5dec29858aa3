{"ast": null, "code": "// Portfolio Configuration\n// Update this file with your personal information\n\nexport const portfolioConfig = {\n  // Personal Information\n  personal: {\n    name: \"Your Name\",\n    title: \"Full Stack Developer & UI/UX Designer\",\n    description: \"I create beautiful, responsive web applications with modern technologies. Passionate about clean code, user experience, and innovative solutions.\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    location: \"Your City, Country\",\n    avatar: \"👨‍💻\" // You can replace this with an image URL\n  },\n  // About Section\n  about: {\n    description: [\"I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. I specialize in modern web technologies and love turning complex problems into simple, beautiful designs.\", \"When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community. I believe in continuous learning and staying up-to-date with the latest industry trends.\"],\n    stats: [{\n      number: \"50+\",\n      label: \"Projects Completed\"\n    }, {\n      number: \"3+\",\n      label: \"Years Experience\"\n    }, {\n      number: \"20+\",\n      label: \"Happy Clients\"\n    }]\n  },\n  // Skills Section\n  skills: [{\n    name: 'JavaScript',\n    level: 90,\n    category: 'Frontend'\n  }, {\n    name: 'TypeScript',\n    level: 85,\n    category: 'Frontend'\n  }, {\n    name: 'React',\n    level: 90,\n    category: 'Frontend'\n  }, {\n    name: 'Vue.js',\n    level: 75,\n    category: 'Frontend'\n  }, {\n    name: 'HTML/CSS',\n    level: 95,\n    category: 'Frontend'\n  }, {\n    name: 'Node.js',\n    level: 80,\n    category: 'Backend'\n  }, {\n    name: 'Python',\n    level: 75,\n    category: 'Backend'\n  }, {\n    name: 'Express.js',\n    level: 80,\n    category: 'Backend'\n  }, {\n    name: 'MongoDB',\n    level: 70,\n    category: 'Database'\n  }, {\n    name: 'PostgreSQL',\n    level: 75,\n    category: 'Database'\n  }, {\n    name: 'Git',\n    level: 85,\n    category: 'Tools'\n  }, {\n    name: 'Docker',\n    level: 70,\n    category: 'Tools'\n  }],\n  // Projects Section\n  projects: [{\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',\n    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],\n    image: '🛒',\n    liveUrl: 'https://your-project-url.com',\n    githubUrl: 'https://github.com/yourusername/project',\n    category: 'Web App'\n  }, {\n    id: 2,\n    title: 'Task Management App',\n    description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n    technologies: ['Vue.js', 'Express.js', 'Socket.io', 'PostgreSQL'],\n    image: '📋',\n    liveUrl: 'https://your-project-url.com',\n    githubUrl: 'https://github.com/yourusername/project',\n    category: 'Web App'\n  }, {\n    id: 3,\n    title: 'Weather Dashboard',\n    description: 'A responsive weather dashboard that displays current weather and forecasts for multiple cities with beautiful data visualizations.',\n    technologies: ['React', 'Chart.js', 'Weather API', 'CSS3'],\n    image: '🌤️',\n    liveUrl: 'https://your-project-url.com',\n    githubUrl: 'https://github.com/yourusername/project',\n    category: 'Frontend'\n  }, {\n    id: 4,\n    title: 'Mobile Banking App',\n    description: 'A React Native mobile application for banking services with biometric authentication and real-time transaction tracking.',\n    technologies: ['React Native', 'Firebase', 'Redux', 'TypeScript'],\n    image: '💳',\n    liveUrl: 'https://your-project-url.com',\n    githubUrl: 'https://github.com/yourusername/project',\n    category: 'Mobile'\n  }],\n  // Social Links\n  social: {\n    linkedin: \"https://linkedin.com/in/yourusername\",\n    github: \"https://github.com/yourusername\",\n    twitter: \"https://twitter.com/yourusername\",\n    instagram: \"https://instagram.com/yourusername\"\n  },\n  // Navigation\n  navigation: [{\n    name: 'Home',\n    id: 'hero'\n  }, {\n    name: 'About',\n    id: 'about'\n  }, {\n    name: 'Skills',\n    id: 'skills'\n  }, {\n    name: 'Projects',\n    id: 'projects'\n  }, {\n    name: 'Contact',\n    id: 'contact'\n  }]\n};\nexport default portfolioConfig;", "map": {"version": 3, "names": ["portfolioConfig", "personal", "name", "title", "description", "email", "phone", "location", "avatar", "about", "stats", "number", "label", "skills", "level", "category", "projects", "id", "technologies", "image", "liveUrl", "githubUrl", "social", "linkedin", "github", "twitter", "instagram", "navigation"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/config/portfolioConfig.ts"], "sourcesContent": ["// Portfolio Configuration\n// Update this file with your personal information\n\nexport const portfolioConfig = {\n  // Personal Information\n  personal: {\n    name: \"Your Name\",\n    title: \"Full Stack Developer & UI/UX Designer\",\n    description: \"I create beautiful, responsive web applications with modern technologies. Passionate about clean code, user experience, and innovative solutions.\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    location: \"Your City, Country\",\n    avatar: \"👨‍💻\", // You can replace this with an image URL\n  },\n\n  // About Section\n  about: {\n    description: [\n      \"I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. I specialize in modern web technologies and love turning complex problems into simple, beautiful designs.\",\n      \"When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community. I believe in continuous learning and staying up-to-date with the latest industry trends.\"\n    ],\n    stats: [\n      { number: \"50+\", label: \"Projects Completed\" },\n      { number: \"3+\", label: \"Years Experience\" },\n      { number: \"20+\", label: \"Happy Clients\" }\n    ]\n  },\n\n  // Skills Section\n  skills: [\n    { name: 'JavaScript', level: 90, category: 'Frontend' },\n    { name: 'TypeScript', level: 85, category: 'Frontend' },\n    { name: 'React', level: 90, category: 'Frontend' },\n    { name: 'Vue.js', level: 75, category: 'Frontend' },\n    { name: 'HTML/CSS', level: 95, category: 'Frontend' },\n    { name: 'Node.js', level: 80, category: 'Backend' },\n    { name: 'Python', level: 75, category: 'Backend' },\n    { name: 'Express.js', level: 80, category: 'Backend' },\n    { name: 'MongoDB', level: 70, category: 'Database' },\n    { name: 'PostgreSQL', level: 75, category: 'Database' },\n    { name: 'Git', level: 85, category: 'Tools' },\n    { name: 'Docker', level: 70, category: 'Tools' },\n  ],\n\n  // Projects Section\n  projects: [\n    {\n      id: 1,\n      title: 'E-Commerce Platform',\n      description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],\n      image: '🛒',\n      liveUrl: 'https://your-project-url.com',\n      githubUrl: 'https://github.com/yourusername/project',\n      category: 'Web App'\n    },\n    {\n      id: 2,\n      title: 'Task Management App',\n      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n      technologies: ['Vue.js', 'Express.js', 'Socket.io', 'PostgreSQL'],\n      image: '📋',\n      liveUrl: 'https://your-project-url.com',\n      githubUrl: 'https://github.com/yourusername/project',\n      category: 'Web App'\n    },\n    {\n      id: 3,\n      title: 'Weather Dashboard',\n      description: 'A responsive weather dashboard that displays current weather and forecasts for multiple cities with beautiful data visualizations.',\n      technologies: ['React', 'Chart.js', 'Weather API', 'CSS3'],\n      image: '🌤️',\n      liveUrl: 'https://your-project-url.com',\n      githubUrl: 'https://github.com/yourusername/project',\n      category: 'Frontend'\n    },\n    {\n      id: 4,\n      title: 'Mobile Banking App',\n      description: 'A React Native mobile application for banking services with biometric authentication and real-time transaction tracking.',\n      technologies: ['React Native', 'Firebase', 'Redux', 'TypeScript'],\n      image: '💳',\n      liveUrl: 'https://your-project-url.com',\n      githubUrl: 'https://github.com/yourusername/project',\n      category: 'Mobile'\n    }\n  ],\n\n  // Social Links\n  social: {\n    linkedin: \"https://linkedin.com/in/yourusername\",\n    github: \"https://github.com/yourusername\",\n    twitter: \"https://twitter.com/yourusername\",\n    instagram: \"https://instagram.com/yourusername\",\n  },\n\n  // Navigation\n  navigation: [\n    { name: 'Home', id: 'hero' },\n    { name: 'About', id: 'about' },\n    { name: 'Skills', id: 'skills' },\n    { name: 'Projects', id: 'projects' },\n    { name: 'Contact', id: 'contact' }\n  ]\n};\n\nexport default portfolioConfig;\n"], "mappings": "AAAA;AACA;;AAEA,OAAO,MAAMA,eAAe,GAAG;EAC7B;EACAC,QAAQ,EAAE;IACRC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,uCAAuC;IAC9CC,WAAW,EAAE,mJAAmJ;IAChKC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,oBAAoB;IAC9BC,MAAM,EAAE,OAAO,CAAE;EACnB,CAAC;EAED;EACAC,KAAK,EAAE;IACLL,WAAW,EAAE,CACX,oOAAoO,EACpO,oPAAoP,CACrP;IACDM,KAAK,EAAE,CACL;MAAEC,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAqB,CAAC,EAC9C;MAAED,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAmB,CAAC,EAC3C;MAAED,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAgB,CAAC;EAE7C,CAAC;EAED;EACAC,MAAM,EAAE,CACN;IAAEX,IAAI,EAAE,YAAY;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACvD;IAAEb,IAAI,EAAE,YAAY;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACvD;IAAEb,IAAI,EAAE,OAAO;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EAClD;IAAEb,IAAI,EAAE,QAAQ;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACnD;IAAEb,IAAI,EAAE,UAAU;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACrD;IAAEb,IAAI,EAAE,SAAS;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,EACnD;IAAEb,IAAI,EAAE,QAAQ;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,EAClD;IAAEb,IAAI,EAAE,YAAY;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,EACtD;IAAEb,IAAI,EAAE,SAAS;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACpD;IAAEb,IAAI,EAAE,YAAY;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACvD;IAAEb,IAAI,EAAE,KAAK;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAQ,CAAC,EAC7C;IAAEb,IAAI,EAAE,QAAQ;IAAEY,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAQ,CAAC,CACjD;EAED;EACAC,QAAQ,EAAE,CACR;IACEC,EAAE,EAAE,CAAC;IACLd,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,oJAAoJ;IACjKc,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;IACvDC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,8BAA8B;IACvCC,SAAS,EAAE,yCAAyC;IACpDN,QAAQ,EAAE;EACZ,CAAC,EACD;IACEE,EAAE,EAAE,CAAC;IACLd,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,mIAAmI;IAChJc,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;IACjEC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,8BAA8B;IACvCC,SAAS,EAAE,yCAAyC;IACpDN,QAAQ,EAAE;EACZ,CAAC,EACD;IACEE,EAAE,EAAE,CAAC;IACLd,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,oIAAoI;IACjJc,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,CAAC;IAC1DC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE,8BAA8B;IACvCC,SAAS,EAAE,yCAAyC;IACpDN,QAAQ,EAAE;EACZ,CAAC,EACD;IACEE,EAAE,EAAE,CAAC;IACLd,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,0HAA0H;IACvIc,YAAY,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC;IACjEC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,8BAA8B;IACvCC,SAAS,EAAE,yCAAyC;IACpDN,QAAQ,EAAE;EACZ,CAAC,CACF;EAED;EACAO,MAAM,EAAE;IACNC,QAAQ,EAAE,sCAAsC;IAChDC,MAAM,EAAE,iCAAiC;IACzCC,OAAO,EAAE,kCAAkC;IAC3CC,SAAS,EAAE;EACb,CAAC;EAED;EACAC,UAAU,EAAE,CACV;IAAEzB,IAAI,EAAE,MAAM;IAAEe,EAAE,EAAE;EAAO,CAAC,EAC5B;IAAEf,IAAI,EAAE,OAAO;IAAEe,EAAE,EAAE;EAAQ,CAAC,EAC9B;IAAEf,IAAI,EAAE,QAAQ;IAAEe,EAAE,EAAE;EAAS,CAAC,EAChC;IAAEf,IAAI,EAAE,UAAU;IAAEe,EAAE,EAAE;EAAW,CAAC,EACpC;IAAEf,IAAI,EAAE,SAAS;IAAEe,EAAE,EAAE;EAAU,CAAC;AAEtC,CAAC;AAED,eAAejB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}