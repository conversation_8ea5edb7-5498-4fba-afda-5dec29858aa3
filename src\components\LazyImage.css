.lazy-image-container {
  position: relative;
  overflow: hidden;
  background: #f7fafc;
  border-radius: 8px;
}

.lazy-image-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(5px);
  transition: opacity 0.3s ease;
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: scale(1.1);
}

.lazy-image.loading {
  opacity: 0;
  transform: scale(1.1);
}

.lazy-image.loaded {
  opacity: 1;
  transform: scale(1);
}

.lazy-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  background: #f7fafc;
  color: #718096;
  text-align: center;
  border: 2px dashed #e2e8f0;
  border-radius: 8px;
}

.lazy-image-error span {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.lazy-image-error p {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 500;
}

.lazy-image-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(102, 126, 234, 0.2);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .lazy-image,
  .lazy-image-placeholder {
    transition: none;
  }
  
  .spinner {
    animation: none;
    border: 3px solid #667eea;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .lazy-image-error {
    border-color: currentColor;
    background: transparent;
  }
}
