{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport './App.css';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Experience from './components/Experience';\nimport Certificates from './components/Certificates';\nimport Projects from './components/Projects';\nimport Contact from './components/Contact';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Experience, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Certificates, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Header", "Hero", "About", "Skills", "Experience", "Certificates", "Projects", "Contact", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './App.css';\nimport LoadingScreen from './components/LoadingScreen';\nimport ScrollProgress from './components/ScrollProgress';\nimport CursorFollower from './components/CursorFollower';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Experience from './components/Experience';\nimport Certificates from './components/Certificates';\nimport Projects from './components/Projects';\nimport Contact from './components/Contact';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <Header />\n      <main>\n        <Hero />\n        <About />\n        <Skills />\n        <Experience />\n        <Certificates />\n        <Projects />\n        <Contact />\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,OAAO,WAAW;AAIlB,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBH,OAAA,CAACT,MAAM;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA;MAAAG,QAAA,gBACEH,OAAA,CAACR,IAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACRP,OAAA,CAACP,KAAK;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACTP,OAAA,CAACN,MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA,CAACL,UAAU;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdP,OAAA,CAACJ,YAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBP,OAAA,CAACH,QAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACZP,OAAA,CAACF,OAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACC,EAAA,GAfQP,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}