{"ast": null, "code": "import React from'react';import'./Hero.css';import{portfolioConfig}from'../config/portfolioConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Hero=()=>{const scrollToSection=sectionId=>{const element=document.getElementById(sectionId);if(element){element.scrollIntoView({behavior:'smooth'});}};return/*#__PURE__*/_jsxs(\"section\",{id:\"hero\",className:\"hero\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"hero-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"hero-content\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"hero-title\",children:[\"Hi, I'm \",/*#__PURE__*/_jsx(\"span\",{className:\"highlight\",children:portfolioConfig.personal.name})]}),/*#__PURE__*/_jsx(\"h2\",{className:\"hero-subtitle\",children:portfolioConfig.personal.title}),/*#__PURE__*/_jsx(\"p\",{className:\"hero-description\",children:portfolioConfig.personal.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"hero-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn btn-primary\",onClick:()=>scrollToSection('projects'),children:\"View My Work\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn btn-secondary\",onClick:()=>scrollToSection('contact'),children:\"Get In Touch\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hero-image\",children:/*#__PURE__*/_jsx(\"div\",{className:\"hero-avatar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"avatar-placeholder\",children:/*#__PURE__*/_jsx(\"span\",{children:portfolioConfig.personal.avatar})})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hero-scroll-indicator\",children:/*#__PURE__*/_jsx(\"div\",{className:\"scroll-arrow\",onClick:()=>scrollToSection('about'),children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u2193\"})})})]});};export default Hero;", "map": {"version": 3, "names": ["React", "portfolioConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Hero", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "id", "className", "children", "personal", "name", "title", "description", "type", "onClick", "avatar"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/Hero.tsx"], "sourcesContent": ["import React from 'react';\nimport './Hero.css';\nimport { portfolioConfig } from '../config/portfolioConfig';\n\nconst Hero: React.FC = () => {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"hero\" className=\"hero\">\n      <div className=\"hero-container\">\n        <div className=\"hero-content\">\n          <h1 className=\"hero-title\">\n            Hi, I'm <span className=\"highlight\">{portfolioConfig.personal.name}</span>\n          </h1>\n          <h2 className=\"hero-subtitle\">\n            {portfolioConfig.personal.title}\n          </h2>\n          <p className=\"hero-description\">\n            {portfolioConfig.personal.description}\n          </p>\n          <div className=\"hero-buttons\">\n            <button\n              type=\"button\"\n              className=\"btn btn-primary\"\n              onClick={() => scrollToSection('projects')}\n            >\n              View My Work\n            </button>\n            <button\n              type=\"button\"\n              className=\"btn btn-secondary\"\n              onClick={() => scrollToSection('contact')}\n            >\n              Get In Touch\n            </button>\n          </div>\n        </div>\n        <div className=\"hero-image\">\n          <div className=\"hero-avatar\">\n            <div className=\"avatar-placeholder\">\n              <span>{portfolioConfig.personal.avatar}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"hero-scroll-indicator\">\n        <div className=\"scroll-arrow\" onClick={() => scrollToSection('about')}>\n          <span>↓</span>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,YAAY,CACnB,OAASC,eAAe,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,eAAe,CAAIC,SAAiB,EAAK,CAC7C,KAAM,CAAAC,OAAO,CAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC,CAClD,GAAIC,OAAO,CAAE,CACXA,OAAO,CAACG,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACF,CAAC,CAED,mBACER,KAAA,YAASS,EAAE,CAAC,MAAM,CAACC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjCX,KAAA,QAAKU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BX,KAAA,QAAKU,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BX,KAAA,OAAIU,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,UACjB,cAAAb,IAAA,SAAMY,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEf,eAAe,CAACgB,QAAQ,CAACC,IAAI,CAAO,CAAC,EACxE,CAAC,cACLf,IAAA,OAAIY,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC1Bf,eAAe,CAACgB,QAAQ,CAACE,KAAK,CAC7B,CAAC,cACLhB,IAAA,MAAGY,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC5Bf,eAAe,CAACgB,QAAQ,CAACG,WAAW,CACpC,CAAC,cACJf,KAAA,QAAKU,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3Bb,IAAA,WACEkB,IAAI,CAAC,QAAQ,CACbN,SAAS,CAAC,iBAAiB,CAC3BO,OAAO,CAAEA,CAAA,GAAMf,eAAe,CAAC,UAAU,CAAE,CAAAS,QAAA,CAC5C,cAED,CAAQ,CAAC,cACTb,IAAA,WACEkB,IAAI,CAAC,QAAQ,CACbN,SAAS,CAAC,mBAAmB,CAC7BO,OAAO,CAAEA,CAAA,GAAMf,eAAe,CAAC,SAAS,CAAE,CAAAS,QAAA,CAC3C,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cACNb,IAAA,QAAKY,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBb,IAAA,QAAKY,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1Bb,IAAA,QAAKY,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCb,IAAA,SAAAa,QAAA,CAAOf,eAAe,CAACgB,QAAQ,CAACM,MAAM,CAAO,CAAC,CAC3C,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cACNpB,IAAA,QAAKY,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCb,IAAA,QAAKY,SAAS,CAAC,cAAc,CAACO,OAAO,CAAEA,CAAA,GAAMf,eAAe,CAAC,OAAO,CAAE,CAAAS,QAAA,cACpEb,IAAA,SAAAa,QAAA,CAAM,QAAC,CAAM,CAAC,CACX,CAAC,CACH,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAV,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}