.footer {
  background: var(--gray-900);
  color: var(--text-inverse);
  padding: var(--space-16) 0 var(--space-8);
  margin-top: var(--space-24);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  margin-bottom: var(--space-12);
}

.footer-brand h3 {
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-inverse);
  margin-bottom: var(--space-3);
}

.footer-brand p {
  color: var(--gray-400);
  font-size: var(--text-base);
  line-height: 1.6;
}

.footer-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
}

.footer-section h4 {
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-inverse);
  margin-bottom: var(--space-4);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--space-2);
}

.footer-section a {
  color: var(--gray-400);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: color var(--duration-normal) var(--ease-out);
}

.footer-section a:hover {
  color: var(--text-inverse);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-8);
  border-top: 1px solid var(--gray-700);
}

.footer-copyright p {
  color: var(--gray-400);
  font-size: var(--text-sm);
  margin: 0;
}

.scroll-to-top {
  background: var(--gray-800);
  border: 1px solid var(--gray-700);
  border-radius: var(--radius-full);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-400);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
}

.scroll-to-top:hover {
  background: var(--gray-700);
  color: var(--text-inverse);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: var(--space-12) 0 var(--space-6);
  }
  
  .footer-content {
    padding: 0 var(--space-4);
  }
  
  .footer-main {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
}
