import React from 'react';
import './Hero.css';
import { portfolioConfig } from '../config/portfolioConfig';

const Hero: React.FC = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-badge">
            <span>👋 Welcome to my portfolio</span>
          </div>
          <h1 className="hero-title">
            Hi, I'm <span className="highlight">{portfolioConfig.personal.name}</span>
          </h1>
          <h2 className="hero-subtitle">
            {portfolioConfig.personal.title}
          </h2>
          <p className="hero-description">
            {portfolioConfig.personal.description}
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">3+</span>
              <span className="stat-label">Years Experience</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">50+</span>
              <span className="stat-label">Projects Completed</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">100%</span>
              <span className="stat-label">Client Satisfaction</span>
            </div>
          </div>
          <div className="hero-buttons">
            <button
              type="button"
              className="btn btn-primary"
              onClick={() => scrollToSection('projects')}
            >
              <span>View My Work</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M7 17L17 7M17 7H7M17 7V17"/>
              </svg>
            </button>
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => scrollToSection('contact')}
            >
              <span>Get In Touch</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z"/>
              </svg>
            </button>
          </div>
        </div>
        <div className="hero-image">
          <div className="hero-avatar">
            <div className="avatar-placeholder">
              <span>{portfolioConfig.personal.avatar}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="hero-scroll-indicator">
        <div className="scroll-arrow" onClick={() => scrollToSection('about')}>
          <span>↓</span>
        </div>
      </div>
    </section>
  );
};

export default Hero;
