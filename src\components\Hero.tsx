import React from 'react';
import './Hero.css';
import { portfolioConfig } from '../config/portfolioConfig';

const Hero: React.FC = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="hero-title">
            Hi, I'm <span className="highlight">{portfolioConfig.personal.name}</span>
          </h1>
          <h2 className="hero-subtitle">
            {portfolioConfig.personal.title}
          </h2>
          <p className="hero-description">
            {portfolioConfig.personal.description}
          </p>
          <div className="hero-buttons">
            <button
              type="button"
              className="btn btn-primary"
              onClick={() => scrollToSection('projects')}
            >
              View My Work
            </button>
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => scrollToSection('contact')}
            >
              Get In Touch
            </button>
          </div>
        </div>
        <div className="hero-image">
          <div className="hero-avatar">
            <div className="avatar-placeholder">
              <span>{portfolioConfig.personal.avatar}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="hero-scroll-indicator">
        <div className="scroll-arrow" onClick={() => scrollToSection('about')}>
          <span>↓</span>
        </div>
      </div>
    </section>
  );
};

export default Hero;
