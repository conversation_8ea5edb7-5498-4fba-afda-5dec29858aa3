/* Luxury Rich Color System */
:root {
  /* Premium Gold & Metallic Colors */
  --gold: #ffd700;
  --gold-light: #ffed4e;
  --gold-dark: #b8860b;
  --gold-darker: #8b6914;

  --platinum: #e5e4e2;
  --platinum-light: #f5f5f3;
  --platinum-dark: #c0bfbd;

  --copper: #b87333;
  --copper-light: #cd853f;
  --copper-dark: #8b4513;

  /* Rich Purple & Royal Colors */
  --royal-purple: #663399;
  --royal-purple-light: #8a2be2;
  --royal-purple-dark: #4b0082;
  --royal-purple-darker: #301934;

  --deep-purple: #2d1b69;
  --deep-purple-light: #4c2a85;
  --deep-purple-dark: #1a0f3d;

  /* Luxury Accent Colors */
  --emerald: #50c878;
  --emerald-light: #66d98a;
  --emerald-dark: #3a9b5c;

  --ruby: #e0115f;
  --ruby-light: #ff1493;
  --ruby-dark: #b8094a;

  --sapphire: #0f52ba;
  --sapphire-light: #1e90ff;
  --sapphire-dark: #0a3d8a;

  /* Luxury Dark Surfaces */
  --black: #000000;
  --rich-black: #0d0d0d;
  --charcoal: #1a1a1a;
  --dark-charcoal: #2d2d2d;
  --gunmetal: #2c3e50;
  --slate: #34495e;
  --midnight: #191970;
  --obsidian: #0f0f23;

  /* Premium Surface System */
  --surface-primary: var(--rich-black);
  --surface-secondary: var(--charcoal);
  --surface-tertiary: var(--dark-charcoal);
  --surface-elevated: var(--gunmetal);
  --surface-luxury: linear-gradient(135deg, var(--rich-black) 0%, var(--charcoal) 50%, var(--dark-charcoal) 100%);
  --surface-glass: rgba(13, 13, 13, 0.85);
  --surface-metallic: linear-gradient(135deg, var(--platinum-dark) 0%, var(--charcoal) 50%, var(--platinum-dark) 100%);

  /* Luxury Text Colors */
  --text-primary: var(--platinum);
  --text-secondary: var(--platinum-dark);
  --text-muted: #a0a0a0;
  --text-inverse: var(--rich-black);
  --text-gold: var(--gold);
  --text-luxury: var(--gold-light);
  --text-accent: var(--emerald);
  --text-royal: var(--royal-purple-light);

  /* Rich Background System */
  --bg-primary: var(--surface-primary);
  --bg-secondary: var(--surface-secondary);
  --bg-tertiary: var(--surface-tertiary);
  --bg-elevated: var(--surface-elevated);
  --bg-glass: var(--surface-glass);
  --bg-luxury: var(--surface-luxury);
  --bg-metallic: var(--surface-metallic);

  /* Dark Theme Shadows & Glows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);

  /* Luxury Glow Effects */
  --glow-gold: 0 0 40px rgba(255, 215, 0, 0.6);
  --glow-gold-intense: 0 0 60px rgba(255, 215, 0, 0.8);
  --glow-platinum: 0 0 30px rgba(229, 228, 226, 0.5);
  --glow-royal: 0 0 35px rgba(102, 51, 153, 0.6);
  --glow-emerald: 0 0 30px rgba(80, 200, 120, 0.5);
  --glow-ruby: 0 0 30px rgba(224, 17, 95, 0.5);
  --glow-sapphire: 0 0 30px rgba(15, 82, 186, 0.5);
  --glow-luxury: 0 0 50px rgba(255, 215, 0, 0.3);

  /* Premium Gradient System */
  --gradient-gold: linear-gradient(135deg, var(--gold-dark) 0%, var(--gold) 50%, var(--gold-light) 100%);
  --gradient-platinum: linear-gradient(135deg, var(--platinum-dark) 0%, var(--platinum) 50%, var(--platinum-light) 100%);
  --gradient-royal: linear-gradient(135deg, var(--royal-purple-dark) 0%, var(--royal-purple) 50%, var(--royal-purple-light) 100%);
  --gradient-emerald: linear-gradient(135deg, var(--emerald-dark) 0%, var(--emerald) 50%, var(--emerald-light) 100%);
  --gradient-ruby: linear-gradient(135deg, var(--ruby-dark) 0%, var(--ruby) 50%, var(--ruby-light) 100%);
  --gradient-sapphire: linear-gradient(135deg, var(--sapphire-dark) 0%, var(--sapphire) 50%, var(--sapphire-light) 100%);

  /* Luxury Multi-Color Gradients */
  --gradient-luxury: linear-gradient(135deg, var(--gold) 0%, var(--royal-purple) 25%, var(--emerald) 50%, var(--sapphire) 75%, var(--ruby) 100%);
  --gradient-wealth: linear-gradient(135deg, var(--gold-dark) 0%, var(--copper) 20%, var(--royal-purple-dark) 40%, var(--emerald-dark) 60%, var(--sapphire-dark) 80%, var(--gold) 100%);
  --gradient-premium: linear-gradient(135deg, var(--platinum) 0%, var(--gold) 30%, var(--royal-purple) 60%, var(--emerald) 100%);

  /* Rich Surface Gradients */
  --gradient-surface: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
  --gradient-elevated: linear-gradient(135deg, var(--surface-tertiary) 0%, var(--surface-elevated) 100%);
  --gradient-metallic: linear-gradient(135deg, var(--charcoal) 0%, var(--gunmetal) 50%, var(--slate) 100%);

  /* Border Radius */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;

  /* Animation Timing */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  height: 100%;
  scroll-padding-top: 80px; /* Account for fixed header */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  background-image:
    radial-gradient(circle at 10% 90%, rgba(255, 215, 0, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(102, 51, 153, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 30% 30%, rgba(80, 200, 120, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(224, 17, 95, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(15, 82, 186, 0.05) 0%, transparent 50%);
  font-weight: 400;
  letter-spacing: -0.011em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 20%, rgba(255, 215, 0, 0.03) 50%, transparent 80%),
    linear-gradient(-45deg, transparent 20%, rgba(102, 51, 153, 0.03) 50%, transparent 80%),
    linear-gradient(135deg, transparent 20%, rgba(80, 200, 120, 0.02) 50%, transparent 80%);
  pointer-events: none;
  z-index: -1;
  animation: luxuryShimmer 20s ease-in-out infinite;
}

body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(255, 215, 0, 0.01) 2px,
      rgba(255, 215, 0, 0.01) 4px
    );
  pointer-events: none;
  z-index: -1;
}

.App {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: var(--bg-primary);
}

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }
}

/* Dark Theme Section Titles */
.section-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: clamp(var(--text-3xl), 5vw, var(--text-5xl));
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-16);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
  line-height: 1.1;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-3);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--glow-primary);
}

/* Clean Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  font-family: inherit;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  text-align: center;
  position: relative;
  letter-spacing: -0.011em;
  min-width: 140px;
  white-space: nowrap;
}

.btn-primary {
  background: var(--gradient-luxury);
  color: var(--text-primary);
  box-shadow: var(--shadow-xl), var(--glow-gold);
  border: 2px solid var(--gold);
  position: relative;
  overflow: hidden;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: var(--text-sm);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 1s ease;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: var(--gradient-wealth);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
  animation: luxuryPulse 3s ease-in-out infinite;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover::after {
  opacity: 0.8;
}

.btn-primary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--glow-gold-intense), 0 0 60px rgba(255, 215, 0, 0.6);
  border-color: var(--gold-light);
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

.btn-primary:active {
  transform: translateY(-2px) scale(1.01);
  box-shadow: var(--shadow-lg), var(--glow-gold);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-platinum);
  border: 2px solid var(--platinum-dark);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: var(--text-sm);
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-platinum);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.btn-secondary::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-royal);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.btn-secondary:hover {
  background: var(--bg-elevated);
  border-color: var(--platinum);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--glow-platinum);
  color: var(--text-primary);
  text-shadow: 0 0 8px rgba(229, 228, 226, 0.6);
}

.btn-secondary:hover::before {
  opacity: 0.15;
}

.btn-secondary:hover::after {
  opacity: 0.3;
}

.btn-secondary:active {
  transform: translateY(-2px) scale(1.01);
  background: var(--bg-tertiary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Responsive Breakpoints */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--space-4);
  }

  .section-title {
    margin-bottom: var(--space-12);
  }

  .btn {
    padding: var(--space-3) var(--space-5);
    font-size: var(--text-sm);
    min-width: 120px;
  }
}

/* Professional Section Spacing */
section {
  position: relative;
}

section:not(#hero) {
  opacity: 0;
  animation: fadeInUp 0.8s var(--ease-out) forwards;
  animation-delay: 0.2s;
}

/* Staggered Animation for Multiple Sections */
section:nth-child(2) { animation-delay: 0.1s; }
section:nth-child(3) { animation-delay: 0.2s; }
section:nth-child(4) { animation-delay: 0.3s; }
section:nth-child(5) { animation-delay: 0.4s; }
section:nth-child(6) { animation-delay: 0.5s; }
section:nth-child(7) { animation-delay: 0.6s; }

/* Fade In Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth Animations */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Luxury Animations */
@keyframes luxuryShimmer {
  0%, 100% {
    opacity: 1;
    transform: translateX(0) translateY(0);
  }
  25% {
    opacity: 0.8;
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    opacity: 0.6;
    transform: translateX(-5px) translateY(10px);
  }
  75% {
    opacity: 0.8;
    transform: translateX(-10px) translateY(-5px);
  }
}

@keyframes luxuryPulse {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

@keyframes goldGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
  }
}

@keyframes floatLuxury {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes luxurySlide {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 0;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  section:not(#hero) {
    opacity: 1;
    animation: none;
  }
}
