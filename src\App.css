/* Universally Appealing Dark Theme Design System */
:root {
  /* Primary Brand Colors - Sophisticated Blue (Trust & Professionalism) */
  --primary: #4f46e5;
  --primary-light: #6366f1;
  --primary-dark: #3730a3;
  --secondary: #7c3aed;
  --accent: #f59e0b;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Sophisticated Dark Theme Palette */
  --gray-50: #0f0f23;
  --gray-100: #1a1a2e;
  --gray-200: #16213e;
  --gray-300: #1f2937;
  --gray-400: #374151;
  --gray-500: #6b7280;
  --gray-600: #9ca3af;
  --gray-700: #d1d5db;
  --gray-800: #e5e7eb;
  --gray-900: #f3f4f6;

  /* Text Colors for Dark Theme */
  --text-primary: #ffffff;
  --text-secondary: var(--gray-800);
  --text-muted: var(--gray-700);
  --text-inverse: var(--gray-50);

  /* Background Colors for Dark Theme */
  --bg-primary: #0a0a0b;
  --bg-secondary: var(--gray-100);
  --bg-tertiary: var(--gray-200);

  /* Dark Theme Shadows & Glows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);

  /* Elegant Glow Effects */
  --glow-primary: 0 0 20px rgba(79, 70, 229, 0.4);
  --glow-secondary: 0 0 20px rgba(124, 58, 237, 0.3);
  --glow-accent: 0 0 20px rgba(245, 158, 11, 0.3);
  --glow-success: 0 0 20px rgba(16, 185, 129, 0.3);

  /* Sophisticated Gradient Backgrounds */
  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--success) 100%);
  --gradient-dark: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  --gradient-hero: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);

  /* Border Radius */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;

  /* Animation Timing */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  height: 100%;
  scroll-padding-top: 80px; /* Account for fixed header */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.04) 0%, transparent 50%);
  font-weight: 400;
  letter-spacing: -0.011em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  overflow-x: hidden;
}

.App {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: var(--bg-primary);
}

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }
}

/* Dark Theme Section Titles */
.section-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: clamp(var(--text-3xl), 5vw, var(--text-5xl));
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-16);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
  line-height: 1.1;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-3);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--glow-primary);
}

/* Clean Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  font-family: inherit;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  text-align: center;
  position: relative;
  letter-spacing: -0.011em;
  min-width: 140px;
  white-space: nowrap;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-md), var(--glow-primary);
  border: 1px solid rgba(0, 212, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-primary);
  border-color: var(--primary);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md), var(--glow-primary);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--gray-400);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-300);
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: var(--primary);
}

.btn-secondary:active {
  transform: translateY(0);
  background: var(--gray-400);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Responsive Breakpoints */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--space-4);
  }

  .section-title {
    margin-bottom: var(--space-12);
  }

  .btn {
    padding: var(--space-3) var(--space-5);
    font-size: var(--text-sm);
    min-width: 120px;
  }
}

/* Professional Section Spacing */
section {
  position: relative;
}

section:not(#hero) {
  opacity: 0;
  animation: fadeInUp 0.8s var(--ease-out) forwards;
  animation-delay: 0.2s;
}

/* Staggered Animation for Multiple Sections */
section:nth-child(2) { animation-delay: 0.1s; }
section:nth-child(3) { animation-delay: 0.2s; }
section:nth-child(4) { animation-delay: 0.3s; }
section:nth-child(5) { animation-delay: 0.4s; }
section:nth-child(6) { animation-delay: 0.5s; }
section:nth-child(7) { animation-delay: 0.6s; }

/* Fade In Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth Animations */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  section:not(#hero) {
    opacity: 1;
    animation: none;
  }
}
