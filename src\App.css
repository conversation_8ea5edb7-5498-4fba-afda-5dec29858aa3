/* Advanced Dark Theme Color System */
:root {
  /* Core Brand Colors - Rich & Sophisticated */
  --primary: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4338ca;
  --primary-darker: #3730a3;

  --secondary: #8b5cf6;
  --secondary-light: #a78bfa;
  --secondary-dark: #7c3aed;
  --secondary-darker: #6d28d9;

  --accent: #06b6d4;
  --accent-light: #22d3ee;
  --accent-dark: #0891b2;
  --accent-darker: #0e7490;

  --success: #10b981;
  --success-light: #34d399;
  --success-dark: #059669;

  --warning: #f59e0b;
  --warning-light: #fbbf24;
  --warning-dark: #d97706;

  --error: #ef4444;
  --error-light: #f87171;
  --error-dark: #dc2626;

  /* Rich Dark Theme Palette */
  --gray-50: #0a0a0f;
  --gray-100: #0f0f1a;
  --gray-200: #1a1a2e;
  --gray-300: #252545;
  --gray-400: #2d2d55;
  --gray-500: #404066;
  --gray-600: #6b7280;
  --gray-700: #9ca3af;
  --gray-800: #d1d5db;
  --gray-900: #f9fafb;

  /* Special Dark Surfaces */
  --surface-primary: #0f0f1a;
  --surface-secondary: #1a1a2e;
  --surface-tertiary: #252545;
  --surface-elevated: #2d2d55;
  --surface-glass: rgba(26, 26, 46, 0.8);

  /* Advanced Text Colors */
  --text-primary: #ffffff;
  --text-secondary: var(--gray-800);
  --text-muted: var(--gray-700);
  --text-inverse: var(--gray-50);
  --text-accent: var(--accent-light);
  --text-success: var(--success-light);
  --text-warning: var(--warning-light);
  --text-error: var(--error-light);

  /* Rich Background System */
  --bg-primary: var(--surface-primary);
  --bg-secondary: var(--surface-secondary);
  --bg-tertiary: var(--surface-tertiary);
  --bg-elevated: var(--surface-elevated);
  --bg-glass: var(--surface-glass);

  /* Dark Theme Shadows & Glows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);

  /* Advanced Glow Effects */
  --glow-primary: 0 0 30px rgba(99, 102, 241, 0.5);
  --glow-secondary: 0 0 30px rgba(139, 92, 246, 0.4);
  --glow-accent: 0 0 30px rgba(6, 182, 212, 0.4);
  --glow-success: 0 0 30px rgba(16, 185, 129, 0.4);
  --glow-warning: 0 0 30px rgba(245, 158, 11, 0.4);
  --glow-soft: 0 0 40px rgba(99, 102, 241, 0.2);

  /* Rich Gradient System */
  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 50%, var(--secondary) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-light) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-light) 100%);
  --gradient-success: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);

  /* Complex Multi-Color Gradients */
  --gradient-hero: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 30%, var(--accent) 70%, var(--success) 100%);
  --gradient-cosmic: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 25%, var(--accent-dark) 50%, var(--primary) 75%, var(--secondary-light) 100%);
  --gradient-aurora: linear-gradient(135deg, var(--accent) 0%, var(--primary) 25%, var(--secondary) 50%, var(--accent-light) 75%, var(--success) 100%);

  /* Surface Gradients */
  --gradient-surface: linear-gradient(135deg, var(--surface-secondary) 0%, var(--surface-tertiary) 100%);
  --gradient-elevated: linear-gradient(135deg, var(--surface-tertiary) 0%, var(--surface-elevated) 100%);

  /* Border Radius */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;

  /* Animation Timing */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  height: 100%;
  scroll-padding-top: 80px; /* Account for fixed header */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  background-image:
    radial-gradient(circle at 15% 85%, rgba(99, 102, 241, 0.12) 0%, transparent 60%),
    radial-gradient(circle at 85% 15%, rgba(139, 92, 246, 0.10) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.05) 0%, transparent 60%);
  font-weight: 400;
  letter-spacing: -0.011em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(139, 92, 246, 0.02) 50%, transparent 70%);
  pointer-events: none;
  z-index: -1;
}

.App {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: var(--bg-primary);
}

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }
}

/* Dark Theme Section Titles */
.section-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: clamp(var(--text-3xl), 5vw, var(--text-5xl));
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-16);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
  line-height: 1.1;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-3);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--glow-primary);
}

/* Clean Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  font-family: inherit;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  text-align: center;
  position: relative;
  letter-spacing: -0.011em;
  min-width: 140px;
  white-space: nowrap;
}

.btn-primary {
  background: var(--gradient-cosmic);
  color: var(--text-primary);
  box-shadow: var(--shadow-lg), var(--glow-primary);
  border: 2px solid rgba(99, 102, 241, 0.4);
  position: relative;
  overflow: hidden;
  font-weight: 600;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left 0.8s ease;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-aurora);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover::after {
  opacity: 0.6;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl), var(--glow-primary), 0 0 40px rgba(99, 102, 241, 0.4);
  border-color: var(--primary-light);
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--glow-primary);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-secondary);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.btn-secondary:hover {
  background: var(--bg-elevated);
  border-color: var(--secondary-light);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg), var(--glow-secondary);
  color: var(--text-primary);
}

.btn-secondary:hover::before {
  opacity: 0.1;
}

.btn-secondary:active {
  transform: translateY(-1px);
  background: var(--bg-tertiary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Responsive Breakpoints */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--space-4);
  }

  .section-title {
    margin-bottom: var(--space-12);
  }

  .btn {
    padding: var(--space-3) var(--space-5);
    font-size: var(--text-sm);
    min-width: 120px;
  }
}

/* Professional Section Spacing */
section {
  position: relative;
}

section:not(#hero) {
  opacity: 0;
  animation: fadeInUp 0.8s var(--ease-out) forwards;
  animation-delay: 0.2s;
}

/* Staggered Animation for Multiple Sections */
section:nth-child(2) { animation-delay: 0.1s; }
section:nth-child(3) { animation-delay: 0.2s; }
section:nth-child(4) { animation-delay: 0.3s; }
section:nth-child(5) { animation-delay: 0.4s; }
section:nth-child(6) { animation-delay: 0.5s; }
section:nth-child(7) { animation-delay: 0.6s; }

/* Fade In Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth Animations */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  section:not(#hero) {
    opacity: 1;
    animation: none;
  }
}
