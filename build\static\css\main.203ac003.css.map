{"version": 3, "file": "static/css/main.203ac003.css", "mappings": "+LAGA,KAEE,yIAEY,CAHZ,QAAS,CAMT,iBACF,CAEA,KACE,uEAEF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kDAA6D,CAC7D,iBACF,CAEA,gCACE,kDACF,CAGA,YACE,oBAAoC,CACpC,aACF,CAEA,iBACE,oBAAoC,CACpC,aACF,CC5CA,MAEE,cAAkB,CAClB,uBAAwB,CACxB,sBAAuB,CACvB,mBAAoB,CACpB,gBAAiB,CACjB,iBAAkB,CAClB,eAAgB,CAGhB,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CAGnB,8BAA+B,CAC/B,gCAAiC,CACjC,4BAA6B,CAC7B,mBAAuB,CAGvB,iBAAqB,CACrB,6BAA8B,CAC9B,6BAA8B,CAG9B,iCAA4C,CAC5C,uDAA4E,CAC5E,6DAAkF,CAClF,+DAAoF,CACpF,iEAAsF,CAGtF,oBAAqB,CACrB,mBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,oBAAqB,CAGrB,iBAAkB,CAClB,gBAAiB,CACjB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAGhB,iBAAkB,CAClB,kBAAmB,CACnB,gBAAiB,CACjB,kBAAmB,CACnB,iBAAkB,CAClB,iBAAkB,CAClB,mBAAoB,CACpB,kBAAmB,CACnB,eAAgB,CAChB,kBAAmB,CACnB,iBAAkB,CAGlB,qBAAsB,CACtB,uBAAwB,CACxB,qBAAsB,CACtB,uCAA2C,CAC3C,kCAAsC,CACtC,iCACF,CAGA,iBAKE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAEE,cAAe,CADf,sBAGF,CAEA,UAHE,WAcF,CAXA,KAQE,kCAAmC,CACnC,iCAAkC,CALlC,qBAAmC,CAAnC,kCAAmC,CADnC,aAA0B,CAA1B,yBAA0B,CAF1B,6EAAyF,CAIzF,eAAgB,CAChB,sBAAwB,CAJxB,eAAgB,CAKhB,iCAIF,CAEA,KAIE,eAA6B,CAA7B,4BAA6B,CAH7B,gBAAiB,CAEjB,iBAAkB,CADlB,iBAGF,CAGA,WAEE,aAAc,CADd,gBAAiB,CAEjB,gBAAyB,CAAzB,wBACF,CAEA,yBACE,WACE,cAAyB,CAAzB,wBACF,CACF,CAGA,eAME,aAA0B,CAA1B,yBAA0B,CAL1B,oCAAwC,CACxC,kCAAuD,CAAvD,oDAAuD,CACvD,eAAgB,CAIhB,sBAAwB,CACxB,eAAgB,CAHhB,kBAA8B,CAA9B,6BAA8B,CAI9B,iBAAkB,CALlB,iBAMF,CAEA,qBAQE,eAA0B,CAA1B,yBAA0B,CAC1B,oBAAiC,CAAjC,gCAAiC,CANjC,sBAAuB,CAFvB,UAAW,CAMX,UAAW,CAHX,QAAS,CAFT,iBAAkB,CAGlB,0BAA2B,CAC3B,UAIF,CAGA,KAEE,kBAAmB,CAGnB,WAAY,CACZ,mBAA+B,CAA/B,8BAA+B,CAK/B,cAAe,CAVf,mBAAoB,CAQpB,mBAAoB,CAFpB,cAA2B,CAA3B,0BAA2B,CAC3B,eAAgB,CALhB,sBAAuB,CAYvB,sBAAwB,CACxB,eAAgB,CAZhB,qBAAsC,CAAtC,qCAAsC,CAUtC,iBAAkB,CADlB,iBAAkB,CAHlB,oBAAqB,CAErB,0CAAsD,CAAtD,qDAAsD,CAKtD,kBACF,CAEA,aACE,eAA0B,CAA1B,yBAA0B,CAE1B,sDAA4B,CAA5B,2BAA4B,CAD5B,UAA0B,CAA1B,yBAEF,CAEA,mBACE,kBAA+B,CAA/B,8BAA+B,CAE/B,4DAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,oBACE,uBAEF,CAEA,mCAHE,gCAA4B,CAA5B,2BAQF,CALA,eACE,gBAAuB,CAEvB,wBAAiC,CAAjC,gCAAiC,CADjC,UAAqB,CAArB,oBAGF,CAEA,qBACE,kBAA0B,CAA1B,yBAA0B,CAC1B,iBAA4B,CAA5B,2BAA4B,CAE5B,sDAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,sBAEE,kBAA2B,CAA3B,0BAA2B,CAD3B,uBAEF,CAGA,aAAe,iBAAoB,CACnC,WAAa,eAAkB,CAC/B,YAAc,gBAAmB,CAEjC,aAAe,eAAkB,CACjC,eAAiB,eAAkB,CACnC,WAAa,eAAkB,CAE/B,cAAgB,aAA0B,CAA1B,yBAA4B,CAC5C,gBAAkB,aAA4B,CAA5B,2BAA8B,CAChD,YAAc,aAAwB,CAAxB,uBAA0B,CAExC,YAAc,qBAAmC,CAAnC,kCAAqC,CACnD,cAAgB,wBAAqC,CAArC,oCAAuC,CAEvD,SAAW,qBAA+B,CAA/B,8BAAiC,CAC5C,YAAc,mBAA+B,CAA/B,8BAAiC,CAC/C,YAAc,oBAA+B,CAA/B,8BAAiC,CAC/C,cAAgB,oBAAiC,CAAjC,gCAAmC,CAEnD,WAAa,sDAA4B,CAA5B,2BAA8B,CAC3C,WAAa,4DAA4B,CAA5B,2BAA8B,CAC3C,WAAa,8DAA4B,CAA5B,2BAA8B,CAG3C,yBACE,WACE,cAAyB,CAAzB,wBACF,CAEA,eACE,kBAA8B,CAA9B,6BACF,CAEA,KAEE,iBAAyB,CAAzB,wBAAyB,CACzB,eAAgB,CAFhB,sBAAsC,CAAtC,qCAGF,CACF,CAGA,8CACE,KACE,sBACF,CACF,CAEA,uCACE,iBAGE,kCAAqC,CACrC,qCAAuC,CAEvC,8BAAgC,CADhC,mCAEF,CACF,CCpRA,gBAQE,kBAAmB,CAFnB,kDAA6D,CAC7D,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CASN,+CAAmD,CAPnD,UAAW,CAMX,YAEF,CAEA,yBACE,SAAU,CACV,iBACF,CAEA,iBAEE,UAAY,CACZ,iBAAkB,CAFlB,iBAAkB,CAGlB,SACF,CAEA,iBAME,oDAAqD,CALrD,kCAAsC,CACtC,cAAe,CACf,eAAgB,CAEhB,qBAAuB,CADvB,kBAGF,CAEA,gBAME,iDAAkD,CAHlD,iDAAoD,CAEpD,iBAAkB,CAHlB,UAAW,CAEX,kBAAmB,CAHnB,WAMF,CAEA,kBACE,kBACF,CAEA,cAGE,gBAAoC,CADpC,UAAW,CAGX,kBAAmB,CACnB,eAAgB,CALhB,WAOF,CAEA,6BANE,iBAAkB,CAGlB,iBASF,CANA,eAEE,iDAAoD,CADpD,WAAY,CAGZ,yBAEF,CAEA,qBAQE,mDAAoD,CADpD,mDAAsF,CADtF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,eACE,oCAAwC,CACxC,gBAAiB,CACjB,eAAgB,CAChB,UACF,CAEA,kBAME,8CAA+C,CAL/C,gBAAiB,CACjB,eAAgB,CAEhB,mBAAqB,CADrB,UAAY,CAEZ,wBAEF,CAEA,mBAKE,WAAY,CAFZ,MAAO,CAGP,eAAgB,CALhB,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SACF,CAEA,UAME,0CAA2C,CAF3C,gBAAoC,CACpC,iBAAkB,CAFlB,UAAW,CAFX,iBAAkB,CAClB,SAKF,CAEA,YAAyB,kBAAmB,CAA9B,QAAgC,CAC9C,YAAyB,mBAAqB,CAAhC,QAAkC,CAChD,YAAyB,kBAAmB,CAA9B,QAAgC,CAC9C,YAAyB,oBAAqB,CAAhC,QAAkC,CAChD,YAAyB,kBAAmB,CAA9B,QAAgC,CAC9C,YAAyB,oBAAqB,CAAhC,QAAkC,CAChD,YAAyB,kBAAmB,CAA9B,QAAgC,CAC9C,YAAyB,oBAAqB,CAAhC,QAAkC,CAChD,YAAyB,kBAAmB,CAA9B,QAAgC,CAC9C,aAA0B,oBAAqB,CAAhC,QAAkC,CACjD,aAA0B,kBAAmB,CAA9B,QAAgC,CAC/C,aAA0B,oBAAqB,CAAhC,QAAkC,CACjD,aAA0B,kBAAmB,CAA9B,QAAgC,CAC/C,aAA0B,oBAAqB,CAAhC,QAAkC,CACjD,aAA0B,kBAAmB,CAA9B,QAAgC,CAC/C,aAA0B,oBAAqB,CAAhC,QAAkC,CACjD,aAA0B,kBAAmB,CAA9B,QAAgC,CAC/C,aAA0B,oBAAqB,CAAhC,QAAkC,CACjD,aAAyB,kBAAmB,CAA7B,OAA+B,CAC9C,aAA0B,oBAAqB,CAAhC,QAAkC,CAGjD,oBACE,GACE,8BAA8C,CAC9C,kBACF,CACA,GACE,0BAA8C,CAC9C,qBACF,CACF,CAEA,2BACE,MACE,mBACF,CACA,IACE,qBACF,CACF,CAEA,2BACE,GACE,2BACF,CACA,GACE,0BACF,CACF,CAEA,wBACE,MACE,UACF,CACA,IACE,SACF,CACF,CAEA,yBACE,GAEE,SAAU,CADV,wCAEF,CACA,IACE,SACF,CACA,IACE,SACF,CACA,GAEE,SAAU,CADV,0CAEF,CACF,CAGA,yBACE,iBACE,cACF,CAEA,cACE,WACF,CAEA,kBACE,cACF,CACF,CC1MA,qBAQE,kCAAmC,CACnC,0BAA2B,CAH3B,oBAAoC,CADpC,UAAW,CAFX,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAGX,YAGF,CAEA,sBAEE,iDAA4D,CAD5D,WAAY,CAGZ,iBAAkB,CADlB,yBAEF,CAEA,4BAQE,8CAA+C,CAD/C,6CAAyE,CANzE,UAAW,CAKX,WAAY,CAJZ,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAEN,UAIF,CAEA,eAYE,kBAAmB,CAQnB,kCAAmC,CACnC,0BAA2B,CAf3B,kDAA6D,CAC7D,WAAY,CACZ,iBAAkB,CANlB,WAAY,CAYZ,+BAA+C,CAL/C,UAAY,CACZ,cAAe,CACf,YAAa,CANb,WAAY,CAQZ,sBAAuB,CAIvB,SAAU,CAhBV,cAAe,CAEf,UAAW,CAaX,2BAA4B,CAD5B,0CAAiD,CAGjD,iBAAkB,CAdlB,UAAW,CAeX,YAGF,CAEA,uBAEE,SAAU,CADV,uBAAwB,CAExB,kBACF,CAEA,qBAEE,gCAAgD,CADhD,qCAEF,CAEA,sBACE,sCACF,CAEA,sBAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,iBAAkB,CAClB,KAAM,CAEN,UAGF,CAEA,0BAEE,WAAY,CACZ,wBAAyB,CAFzB,UAGF,CAEA,mBAEE,WAAY,CAEZ,iBAAkB,CAHlB,UAAW,CAEX,SAEF,CAGA,wBACE,MACE,UACF,CACA,IACE,SACF,CACF,CAGA,yBACE,eACE,aAAc,CAGd,WAAY,CAFZ,YAAa,CACb,UAEF,CAEA,mBAEE,WAAY,CADZ,UAEF,CAEA,0BAEE,WAAY,CADZ,UAEF,CACF,CCtHA,yBACE,EACE,qBACF,CACF,CAEA,iBAGE,WAAY,CAKZ,yBAA0B,CAJ1B,mBAAoB,CAHpB,cAAe,CAKf,8BAAgC,CAChC,uBAAyB,CALzB,UAAW,CAGX,YAIF,CAEA,cAGE,qBAAuB,CACvB,iBAAkB,CAFlB,WAAY,CAIZ,iBAAkB,CADlB,0CAAiD,CAJjD,UAMF,CAEA,qBAOE,eAAiB,CANjB,UAAW,CAGX,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAOR,uBACF,CAEA,mCALE,iBAAkB,CAFlB,UAAW,CAGX,8BAAgC,CAJhC,SAmBF,CAXA,cAIE,kDAA6D,CAM7D,UAAY,CAJZ,mBAAoB,CALpB,cAAe,CAQf,wBAA0B,CAF1B,YAIF,CAGA,0BACE,yCACF,CAEA,wCAEE,oBAAoC,CADpC,oBAEF,CAEA,+CACE,kBAAmB,CACnB,yCACF,CAGA,0BACE,wCACF,CAEA,wCAEE,oBAAmC,CADnC,oBAEF,CAEA,+CACE,kBAAmB,CACnB,uCACF,CAGA,8CAUE,+CAAgD,CAHhD,wBAAyB,CACzB,iBAAkB,CAPlB,UAAW,CAKX,WAAY,CAFZ,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAMR,8BAAgC,CAJhC,UAMF,CAEA,uBACE,GAEE,SAAU,CADV,uCAEF,CACA,GAEE,SAAU,CADV,uCAEF,CACF,CAGA,yBACE,+BAEE,YACF,CAEA,EACE,qBACF,CACF,CCtHA,sBAaE,kBAAmB,CANnB,kDAA6D,CAC7D,WAAY,CACZ,2BAA4B,CAM5B,2BAAyC,CALzC,UAAY,CACZ,cAAe,CACf,YAAa,CANb,WAAY,CAQZ,sBAAuB,CAXvB,MAAO,CAFP,cAAe,CACf,OAAQ,CAER,0BAA2B,CAY3B,uBAAyB,CAXzB,UAAW,CAYX,YACF,CAEA,4BAEE,+BAAyC,CADzC,0CAEF,CAEA,4BACE,yBAA0B,CAC1B,kBACF,CAEA,qBAME,eAAiB,CACjB,+BAAyC,CAFzC,YAAa,CAFb,WAAY,CAOZ,eAAgB,CAThB,cAAe,CACf,KAAM,CAMN,2CAAkD,CAJlD,WAAY,CAKZ,YAEF,CAEA,0BACE,MACF,CAEA,sBAGE,kBAAmB,CAEnB,kDAA6D,CAC7D,UAAY,CALZ,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,yBAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,WASE,kBAAmB,CARnB,eAAgB,CAChB,WAAY,CASZ,iBAAkB,CARlB,UAAY,CAEZ,cAAe,CAGf,YAAa,CAJb,gBAAiB,CAGjB,WAAY,CAGZ,sBAAuB,CAEvB,8BAAgC,CANhC,UAOF,CAEA,iBACE,gBACF,CAEA,iBACE,sBAAwB,CACxB,kBACF,CAEA,uBACE,cACF,CAEA,qBACE,kBACF,CAEA,wBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,SAEF,CAEA,sBAGE,eAAiB,CADjB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAEd,cAAe,CACf,eAAgB,CANhB,kBAAoB,CAOpB,uBACF,CAEA,2CACE,oBAAqB,CACrB,aACF,CAEA,+BAEE,kBAAmB,CADnB,UAEF,CAEA,4BACE,yBAA0B,CAC1B,kBACF,CAEA,mBAEE,kBAAmB,CACnB,iBAAkB,CAElB,aAAc,CADd,eAAgB,CAEhB,cAAe,CALf,kBAAoB,CAMpB,iBACF,CAEA,wBAEE,kBAAmB,CAInB,aAAc,CAHd,cAAe,CAFf,YAAa,CAIb,eAAgB,CADhB,mBAGF,CAEA,8BAGE,cAAe,CADf,SAAU,CADV,iBAGF,CAEA,WAGE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAHlB,WAAY,CAIZ,mBAAqB,CACrB,iBAAkB,CAClB,uBAAyB,CAPzB,UAQF,CAEA,iDACE,kBAAmB,CACnB,oBACF,CAEA,uDAQE,iBAAyB,CAAzB,wBAAyB,CAPzB,UAAW,CAKX,WAAY,CAHZ,QAAS,CADT,iBAAkB,CAElB,OAAQ,CAKR,uBAAwB,CAJxB,SAKF,CAEA,+CACE,yBAA0B,CAC1B,kBACF,CAEA,oBAGE,aAAc,CADd,iBAAmB,CAGnB,eAAgB,CADhB,oBAEF,CAEA,uBAME,oBAA8B,CAD9B,WAAY,CAFZ,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAGX,YACF,CAGA,oBACE,oDAAqE,CACrE,mBAAuB,CACvB,qBAAyB,CACzB,iBAAqB,CACrB,mBACF,CAEA,0BACE,yBAA8B,CAC9B,oBACF,CAEA,yGAGE,oBACF,CAGA,uBACE,kCAAqC,CACrC,qCAAuC,CAEvC,8BAAgC,CADhC,mCAEF,CAGA,yBACE,qBAEE,UAAW,CADX,UAEF,CAEA,sBAEE,WAAY,CADZ,UAEF,CACF,CCzPA,QAME,kCAAmC,CACnC,0BAA2B,CAF3B,oBAAqC,CAKrC,uCAAwC,CAPxC,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAON,qDAAsD,CADtD,YAGF,CAEA,iBACE,oBAAqC,CAErC,uCAAwC,CADxC,2BAEF,CAEA,kBAME,kBAAmB,CAFnB,YAAa,CAGb,WAAY,CAFZ,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,wBAKF,CAEA,SAIE,yBAA0B,CAH1B,oCAAwC,CACxC,wBAAyB,CACzB,eAAgB,CAEhB,sBAAwB,CACxB,iBAAkB,CAClB,uDACF,CAEA,eACE,oBACF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,kBACF,CAEA,QACE,YAAa,CAEb,kBAAmB,CADnB,eAEF,CAEA,YACE,eAAgB,CAChB,WAAY,CAMZ,8BAA+B,CAL/B,2BAA4B,CAG5B,cAAe,CAFf,wBAAyB,CACzB,eAAgB,CAMhB,sBAAwB,CAJxB,qCAAsC,CAEtC,iBAAkB,CAClB,qDAEF,CAEA,kBAEE,0BAA2B,CAD3B,yBAEF,CAEA,kBACE,gCAAiC,CACjC,kBACF,CAEA,oBAGE,eAAgB,CAChB,WAAY,CAGZ,8BAA+B,CAF/B,cAAe,CAJf,YAAa,CACb,qBAAsB,CAItB,sBAAuB,CAEvB,4DACF,CAEA,0BACE,0BACF,CAEA,yBAGE,gCAAiC,CAGjC,gCAAiC,CAJjC,UAAW,CAEX,YAAa,CACb,qDAAsD,CAJtD,UAMF,CAGA,yBACE,kBACE,wBACF,CAEA,oBACE,YACF,CAEA,KAKE,4BAA6B,CAC7B,oCAAqC,CACrC,2BAA4B,CAJ5B,MAAO,CAMP,SAAU,CARV,iBAAkB,CAGlB,OAAQ,CAFR,QAAS,CAMT,2BAA4B,CAG5B,qDAAsD,CADtD,iBAEF,CAEA,cAEE,SAAU,CADV,uBAAwB,CAExB,kBACF,CAEA,QACE,qBAAsB,CAEtB,KAAM,CADN,wBAEF,CAEA,YAIE,eAAgB,CAChB,0BAA2B,CAJ3B,qCAAsC,CACtC,eAAgB,CAChB,UAGF,CAEA,kBACE,yBACF,CAEA,gBACE,kBACF,CAEA,4CACE,4CACF,CAEA,6CACE,SACF,CAEA,6CACE,4CACF,CACF,CCtKA,kBAEE,kBAAmB,CAEnB,eAAgB,CAChB,WAAY,CAGZ,kBAAmB,CAEnB,yBAA0B,CAJ1B,cAAe,CALf,YAAa,CAEb,UAAY,CAIZ,aAAe,CAEf,uBAEF,CAEA,wBACE,oBACF,CAEA,wBACE,yBAA0B,CAC1B,kBACF,CAEA,cAGE,kBAAmB,CACnB,kBAAmB,CAGnB,oCAA8C,CAL9C,WAAY,CAGZ,iBAAkB,CAClB,0CAAiD,CALjD,UAOF,CAEA,qCACE,kBACF,CAEA,cAWE,kBAAmB,CARnB,eAAiB,CACjB,iBAAkB,CAKlB,0BAAwC,CACxC,YAAa,CARb,WAAY,CAUZ,sBAAuB,CALvB,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,0CAAiD,CAPjD,UAYF,CAEA,qCAEE,kBAAmB,CADnB,0BAEF,CAEA,aACE,aAAc,CACd,uBACF,CAEA,oCACE,aACF,CAEA,cACE,iBAAmB,CACnB,eAAgB,CAChB,wBAAyB,CACzB,gBACF,CAGA,gBACE,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CACrB,oBAAqB,CAErB,oBAAqB,CACrB,sBAAuB,CACvB,qBAAsB,CAEtB,0DAAqE,CACrE,4DAAuE,CACvE,yDAAoE,CACpE,uDACF,CAEA,qBACE,kCAAmC,CACnC,yBACF,CAGA,wBACE,oBAAkC,CAClC,iCACF,CAEA,iCACE,oBAAkC,CAClC,gDAGF,CAEA,sBACE,kDACF,CAEA,uBACE,4BACF,CAEA,wBACE,8BACF,CAEA,4BACE,4BACF,CAEA,8BACE,8BACF,CAEA,0BACE,4BACF,CAEA,yBACE,kCACF,CAGA,oIAIE,6BAA8B,CAE9B,gCAA0C,CAD1C,yBAEF,CAEA,4JAIE,4BACF,CAGA,8BACE,oBAAiC,CACjC,0BACF,CAEA,uEAEE,oBAAiC,CACjC,sBAA0C,CAC1C,yBACF,CAEA,mFAGE,oBAAiC,CADjC,kBAEF,CAGA,qCACE,8BAA+B,CAC/B,yBACF,CAEA,wCACE,yBACF,CAEA,sCAEE,kBAEF,CAEA,yEALE,6BAA8B,CAE9B,yBAMF,CAEA,2BACE,6BAA8B,CAC9B,kBACF,CAGA,gCACE,kDACF,CAGA,EACE,yEACF,CAGA,yBACE,kBACE,SACF,CAEA,cAEE,WAAY,CADZ,UAEF,CAEA,cAEE,WAAY,CADZ,UAEF,CAEA,qCACE,0BACF,CAEA,cACE,eACF,CACF,CCtOA,MAGE,kBAAmB,CACnB,4BAA6B,CAF7B,YAAa,CADb,gBAAiB,CAKjB,eAAgB,CADhB,iBAEF,CAEA,aAOE,wEAA4E,CAC5E,gDAAuD,CAPvD,UAAW,CAKX,WAAY,CAJZ,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAEN,SAAU,CAIV,SACF,CAEA,gBAME,wBAAoB,CACpB,kBAAmB,CAHnB,YAAa,CAEb,mBAAoB,CADpB,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CASjB,gBAAiB,CAPjB,wBAAyB,CAKzB,iBAAkB,CAClB,SAEF,CAEA,cAEE,sDAAyD,CADzD,SAEF,CAEA,YAME,yBAA0B,CAL1B,oCAAwC,CACxC,oDAAuD,CACvD,eAAgB,CAIhB,sBAAwB,CAHxB,eAAgB,CAChB,4BAGF,CAEA,WACE,oBAAqB,CACrB,iBACF,CAEA,eAGE,2BAA4B,CAF5B,mDAAsD,CACtD,eAAgB,CAGhB,sBAAwB,CADxB,4BAEF,CAEA,kBAEE,uBAAwB,CADxB,oDAAuD,CAGvD,eAAgB,CADhB,4BAA6B,CAE7B,eACF,CAEA,cACE,YAAa,CAEb,cAAe,CADf,kBAEF,CAEA,YAGE,kBAAmB,CAEnB,uDAA0D,CAJ1D,YAAa,CACb,sBAAuB,CAEvB,SAEF,CAEA,aACE,iBACF,CAEA,oBAME,kBAAmB,CAFnB,0BAA2B,CAQ3B,gCAAiC,CATjC,+BAAgC,CAMhC,2BAA4B,CAJ5B,YAAa,CAGb,cAAe,CANf,YAAa,CAKb,sBAAuB,CAIvB,eAAgB,CADhB,iBAAkB,CATlB,WAYF,CAEA,2BAOE,yEAA6E,CAD7E,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,uBAEE,qBAAsB,CACtB,QAAS,CAFT,iBAAkB,CAGlB,0BAA2B,CAC3B,SACF,CAEA,cAIE,4BAA6B,CAI7B,4BAA6B,CAE7B,gCAAiC,CAHjC,gCAAiC,CAEjC,2BAA4B,CAN5B,uBAAwB,CAFxB,cAAe,CACf,yBAA0B,CAI1B,sBAAuB,CADvB,uDAMF,CAEA,oBAGE,2BAA4B,CAF5B,oBAAqB,CACrB,0BAEF,CAyBA,kBACE,kBACE,uBACF,CACA,IACE,0BACF,CACA,IACE,0BACF,CACF,CAGA,yBACE,aACE,YACF,CAEA,gBAEE,mBAAoB,CADpB,yBAA0B,CAG1B,sBAAuB,CADvB,iBAEF,CAEA,cACE,OACF,CAEA,YACE,OACF,CAEA,oBAGE,cAAe,CADf,YAAa,CADb,WAGF,CAEA,cAEE,kBAAmB,CADnB,sBAEF,CAEA,mBACE,QAAO,CACP,cACF,CACF,CAEA,yBACE,gBACE,kBACF,CAEA,oBAGE,cAAe,CADf,YAAa,CADb,WAGF,CAEA,cACE,qBACF,CACF,CCpOA,OAEE,4BAA6B,CAD7B,yBAEF,CAEA,eAGE,wBAAoB,CACpB,kBAAmB,CAHnB,YAAa,CAEb,mBAAoB,CADpB,6BAGF,CAEA,YAEE,sDAAyD,CADzD,SAEF,CAEA,cAEE,2BAA4B,CAD5B,wBAAyB,CAGzB,eAAgB,CADhB,4BAEF,CAEA,aAGE,uBAAmB,CAFnB,YAAa,CAEb,kBAAmB,CADnB,wDAA2D,CAE3D,yBACF,CAEA,MAGE,8BAA+B,CAE/B,gCAAiC,CADjC,8BAA+B,CAF/B,sBAAuB,CADvB,iBAAkB,CAKlB,qDACF,CAEA,YAGE,4BAA6B,CAD7B,2BAA4B,CAD5B,0BAGF,CAEA,SAGE,oBAAqB,CAErB,oCAAwC,CAJxC,yBAA0B,CAC1B,eAAgB,CAEhB,4BAEF,CAEA,QAEE,uBAAwB,CADxB,wBAAyB,CAEzB,eAAgB,CAChB,QACF,CAEA,aAKE,uDAA0D,CAD1D,SAEF,CAEA,gCALE,kBAAmB,CAFnB,YAAa,CACb,sBAiBF,CAXA,mBAGE,8BAA+B,CAM/B,iCAAkC,CALlC,+BAAgC,CAEhC,qBAAsB,CAJtB,YAAa,CAQb,qDAAsD,CATtD,WAUF,CAEA,yBAEE,0BAA2B,CAD3B,2BAA4B,CAG5B,2BAA4B,CAD5B,0BAEF,CAEA,wBACE,yBAA0B,CAC1B,4BACF,CAEA,qBACE,uBAAwB,CAGxB,wBAAyB,CAFzB,eAAgB,CAChB,QAEF,CAGA,uBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,yBACE,OACE,yBACF,CAEA,eAEE,mBAAoB,CADpB,yBAEF,CAEA,aAEE,kBAAmB,CADnB,mCAEF,CAEA,MACE,sBACF,CAEA,SACE,yBACF,CAEA,mBAEE,YAAa,CADb,WAEF,CAEA,wBACE,yBACF,CACF,CAEA,yBACE,aACE,yBACF,CAEA,mBAEE,YAAa,CADb,WAEF,CACF,CCvKA,QAEE,8BAA+B,CAD/B,yBAEF,CAEA,gBAGE,uBAAmB,CAFnB,YAAa,CAEb,kBAAmB,CADnB,wDAEF,CAEA,gBAQE,gDAAkD,CAPlD,4BAA6B,CAI7B,gCAAiC,CAFjC,8BAA+B,CAC/B,2BAA4B,CAG5B,SAAU,CALV,sBAAuB,CAIvB,qDAGF,CAEA,sBAGE,4BAA6B,CAD7B,2BAA4B,CAD5B,0BAGF,CAEA,gBAIE,yBAA0B,CAH1B,oCAAwC,CACxC,wBAAyB,CACzB,eAAgB,CAEhB,4BAA6B,CAE7B,iBAAkB,CADlB,iBAEF,CAEA,sBAQE,yBAA0B,CAC1B,gCAAiC,CANjC,sBAAuB,CAFvB,UAAW,CAMX,UAAW,CAHX,QAAS,CAFT,iBAAkB,CAGlB,0BAA2B,CAC3B,UAIF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,kBACF,CAEA,YACE,iBACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,4BACF,CAEA,YAEE,2BAA4B,CAC5B,wBAAyB,CAFzB,eAGF,CAEA,kBAEE,oBAAqB,CAErB,oCAAwC,CADxC,wBAAyB,CAFzB,eAIF,CAEA,WAEE,0BAA2B,CAD3B,UAAW,CAGX,eAEF,CAEA,2BALE,gCAAiC,CAEjC,iBASF,CANA,gBAEE,yBAA0B,CAD1B,WAAY,CAGZ,qCAEF,CAEA,sBAQE,6BAA8B,CAD9B,uDAAsF,CADtF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAGA,qBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,mBACE,GACE,2BACF,CACA,GACE,0BACF,CACF,CAGA,4BACE,kBACF,CAEA,6BACE,mBACF,CAEA,6BACE,mBACF,CAEA,6BACE,mBACF,CAGA,yBACE,QACE,yBACF,CAEA,gBAEE,kBAAmB,CADnB,yBAEF,CAEA,gBACE,sBACF,CAEA,gBACE,wBACF,CAMA,8BACE,wBACF,CACF,CC3KA,YAEE,eAAmB,CADnB,cAEF,CAEA,qBAGE,aAAc,CADd,gBAAiB,CADjB,iBAGF,CAEA,4BAOE,kDAA6D,CAF7D,QAAS,CAJT,UAAW,CAEX,QAAS,CADT,iBAAkB,CAElB,KAAM,CAIN,0BAA2B,CAF3B,SAGF,CAEA,iBAIE,8BAA+B,CAF/B,kBAAmB,CADnB,iBAAkB,CAElB,SAEF,CAEA,sBACE,MAAO,CACP,kBACF,CAEA,uBACE,QAAS,CACT,iBACF,CAEA,oBACE,eAAiB,CAEjB,kBAAmB,CACnB,gCAA0C,CAF1C,YAAa,CAGb,iBAAkB,CAClB,uBACF,CAEA,0BAEE,gCAA2C,CAD3C,0BAEF,CAEA,iDAQE,uBAAwB,CAAxB,sBAAwB,CAPxB,UAAW,CAKX,QAAS,CAJT,iBAAkB,CAClB,WAAY,CACZ,QAAS,CACT,OAIF,CAEA,kDAQE,uBAAyB,CAAzB,uBAAyB,CAPzB,UAAW,CAKX,QAAS,CAHT,UAAW,CADX,iBAAkB,CAElB,QAAS,CACT,OAIF,CAEA,mBACE,oBACF,CAEA,UAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,mBACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,oBACF,CAEA,SAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,UAEE,aAAc,CADd,eAAiB,CAEjB,eACF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,UAEE,aAAc,CADd,eAAiB,CAEjB,eACF,CAEA,MAEE,kBAAmB,CACnB,eAAiB,CACjB,eAAgB,CAHhB,qBAAwB,CAIxB,wBACF,CAEA,gBACE,kBAAmB,CACnB,aACF,CAEA,gBACE,kBAAmB,CACnB,aACF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,iBACE,kBAAmB,CACnB,aACF,CAEA,2BACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,2BAIE,aAAc,CACd,eAAgB,CAFhB,oBAAsB,CADtB,mBAAoB,CADpB,iBAKF,CAEA,kCAIE,aAAc,CAHd,WAAY,CAIZ,eAAiB,CAFjB,MAAO,CADP,iBAIF,CAEA,yBAGE,4BAA6B,CAF7B,iBAAkB,CAClB,kBAEF,CAEA,4BAGE,aAAc,CAFd,eAAiB,CACjB,eAAgB,CAEhB,oBACF,CAEA,WACE,YAAa,CACb,cAAe,CACf,SACF,CAEA,UAIE,kBAIF,CAEA,mBAIE,WAAY,CAHZ,iBAAkB,CAClB,QAAS,CACT,UAEF,CAEA,yCACE,WACF,CAEA,0CACE,UACF,CAEA,YAGE,kDAA6D,CAE7D,qBAAuB,CADvB,iBAAkB,CAElB,8BAA8C,CAJ9C,WAAY,CADZ,UAMF,CAeA,6BACE,kBACF,CAEA,8BACE,mBACF,CAEA,8BACE,mBACF,CAEA,8BACE,mBACF,CAGA,yBACE,YACE,cACF,CAEA,4BACE,SACF,CAEA,iBAEE,gBAAkB,CAClB,2BAA6B,CAC7B,4BAA8B,CAH9B,UAIF,CAEA,2BACE,YACF,CAEA,mBACE,mBAAqB,CACrB,oBACF,CAEA,cAGE,UACF,CAEA,6BAJE,sBAAuB,CADvB,qBASF,CAJA,eAGE,SACF,CAEA,UACE,gBACF,CAEA,SACE,gBACF,CACF,CCvSA,cAEE,kDAA6D,CAD7D,cAEF,CAEA,qBACE,YAAa,CAIb,cAAe,CAFf,QAAS,CADT,sBAAuB,CAEvB,kBAEF,CAwBA,mBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,kBAME,8BAA+B,CAL/B,eAAiB,CACjB,kBAAmB,CAEnB,gCAA0C,CAD1C,eAAgB,CAIhB,iBAAkB,CAFlB,uBAGF,CAEA,wBAEE,gCAA2C,CAD3C,2BAEF,CAEA,oBAKE,kBAAmB,CAFnB,kDAA6D,CAC7D,YAAa,CAFb,YAAa,CAIb,sBAAuB,CALvB,iBAMF,CAEA,2BAOE,6EAAgI,CAChI,yBAA0B,CAF1B,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,mBAEE,iBAAkB,CADlB,SAEF,CAEA,WAEE,UAAY,CADZ,cAAe,CAEf,gCACF,CAEA,mBACE,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,SACF,CAEA,gBAOE,kCAAmC,CACnC,0BAA2B,CAP3B,gBAAoC,CAQpC,0BAA0C,CAL1C,kBAAmB,CAFnB,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,kBAOF,CAEA,qBACE,YACF,CAEA,kBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CADhB,mBAEF,CAEA,oBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,mBACF,CAEA,kBAEE,aAAc,CADd,eAAiB,CAGjB,eAAgB,CADhB,kBAEF,CAEA,yBACE,aAAc,CAGd,gBAAkB,CAFlB,eAAgB,CAChB,oBAEF,CAEA,uBAGE,aAAc,CAFd,eAAiB,CACjB,eAAgB,CAEhB,oBACF,CAEA,aACE,YAAa,CACb,cAAe,CACf,SAAW,CACX,oBACF,CAEA,WACE,kDAA6D,CAM7D,wBAAyB,CAHzB,kBAAmB,CAFnB,aAAc,CAGd,eAAiB,CACjB,eAAgB,CAHhB,qBAKF,CAEA,qBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,gBAEF,CAEA,eAEE,aAAc,CADd,eAEF,CAEA,sBACE,aACF,CAEA,YACE,kDAA6D,CAG7D,kBAAmB,CAFnB,UAAY,CAIZ,eAAiB,CACjB,eAAgB,CAJhB,kBAAoB,CAEpB,oBAAqB,CAGrB,uBACF,CAEA,kBAEE,+BAA+C,CAD/C,0BAEF,CAEA,iBAGE,aAAc,CACd,gBAAiB,CAFjB,YAAa,CADb,iBAIF,CAeA,8BACE,kBACF,CAEA,+BACE,mBACF,CAEA,+BACE,mBACF,CAEA,+BACE,mBACF,CAEA,+BACE,mBACF,CAEA,+BACE,kBACF,CAGA,yBACE,cACE,cACF,CAEA,mBAEE,UAAW,CADX,yBAEF,CAEA,qBACE,cACF,CAEA,kBACE,gBACF,CAEA,qBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,YACE,iBACF,CAEA,YAEE,eAAiB,CADjB,kBAEF,CACF,CClRA,UAEE,eAAmB,CADnB,cAEF,CAEA,iBACE,YAAa,CAIb,cAAe,CAFf,QAAS,CADT,sBAAuB,CAEvB,kBAEF,CAEA,YAGE,eAAiB,CADjB,wBAAyB,CAGzB,kBAAmB,CADnB,aAAc,CAGd,cAAe,CADf,eAAgB,CALhB,qBAAuB,CAOvB,uBACF,CAEA,kBACE,oBAAqB,CACrB,aACF,CAEA,mBACE,kDAA6D,CAE7D,kBAAyB,CADzB,UAEF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,cAME,8BAA+B,CAL/B,eAAiB,CACjB,kBAAmB,CAEnB,gCAA0C,CAD1C,eAAgB,CAEhB,uBAEF,CAEA,oBAEE,gCAA2C,CAD3C,2BAEF,CAEA,eAIE,kBAAmB,CAFnB,kDAA6D,CAC7D,YAAa,CAFb,YAAa,CAIb,sBAAuB,CAEvB,eAAgB,CADhB,iBAEF,CAEA,sBAOE,6EAAgI,CAChI,yBAA0B,CAF1B,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,eACE,cAAe,CAEf,iBAAkB,CADlB,SAEF,CAEA,iBACE,YACF,CAEA,eAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,kBACF,CAEA,qBACE,aAAc,CACd,eAAgB,CAChB,oBACF,CAEA,sBACE,YAAa,CACb,cAAe,CACf,SAAW,CACX,oBACF,CAEA,UACE,kDAA6D,CAM7D,wBAAyB,CAHzB,kBAAmB,CAFnB,aAAc,CAGd,eAAiB,CACjB,eAAgB,CAHhB,qBAKF,CAEA,eACE,YAAa,CACb,QACF,CAEA,cAEE,kBAAmB,CAKnB,QAAO,CAHP,eAAgB,CAHhB,qBAAuB,CAKvB,iBAAkB,CAHlB,oBAAqB,CAErB,uBAGF,CAEA,0BACE,kDAA6D,CAC7D,UACF,CAEA,gCAEE,+BAA+C,CAD/C,0BAEF,CAEA,yBACE,gBAAuB,CAEvB,wBAAyB,CADzB,aAEF,CAEA,+BACE,kBAAmB,CACnB,UACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,0BACE,kBACF,CAEA,2BACE,mBACF,CAEA,2BACE,mBACF,CAEA,2BACE,mBACF,CAGA,yBACE,UACE,cACF,CAEA,eAEE,UAAW,CADX,yBAEF,CAEA,iBACE,cACF,CAEA,eACE,gBACF,CAEA,eACE,qBACF,CAEA,YAEE,eAAiB,CADjB,kBAEF,CACF,CCjNA,SAEE,kDAA6D,CAD7D,cAGF,CAEA,iCAHE,UAKF,CAEA,8BACE,eACF,CAEA,iBAGE,aAAS,CACT,iBAAkB,CAHlB,YAAa,CAEb,QAAS,CADT,6BAGF,CAEA,cACE,gCACF,CAEA,iBACE,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,gBACE,gBAAiB,CACjB,eAAgB,CAEhB,UACF,CAEA,iCAJE,kBAMF,CAEA,cAGE,QAAS,CACT,oBACF,CAEA,4BALE,kBAAmB,CADnB,YAiBF,CAXA,cASE,kCAAmC,CACnC,0BAA2B,CAN3B,gBAAoC,CACpC,iBAAkB,CAJlB,gBAAiB,CAEjB,WAAY,CAKZ,sBAAuB,CANvB,UASF,CAEA,iBACE,gBAAiB,CACjB,eAAgB,CAChB,oBACF,CAEA,gBAEE,QAAS,CADT,UAEF,CAEA,cACE,YAAa,CACb,QACF,CAEA,aAME,kBAAmB,CAKnB,kCAAmC,CACnC,0BAA2B,CAT3B,gBAAoC,CACpC,iBAAkB,CAClB,YAAa,CAIb,gBAAiB,CAPjB,WAAY,CAKZ,sBAAuB,CACvB,oBAAqB,CAErB,uBAAyB,CATzB,UAYF,CAEA,mBACE,oBAAoC,CACpC,0BACF,CAEA,cAOE,0CAA4C,CAH5C,kCAAmC,CACnC,0BAA2B,CAJ3B,oBAAoC,CAKpC,sBAA0C,CAH1C,kBAAmB,CADnB,YAMF,CAEA,YACE,oBACF,CAEA,uCAUE,kCAAmC,CACnC,0BAA2B,CAL3B,oBAAoC,CAFpC,0BAA0C,CAC1C,kBAAmB,CAEnB,UAAY,CACZ,cAAe,CALf,YAAa,CAMb,uBAAyB,CAPzB,UAUF,CAEA,iEAEE,eACF,CAEA,mDAIE,gBAAoC,CADpC,kBAAsC,CADtC,YAGF,CAEA,YAGE,eAAiB,CAEjB,WAAY,CACZ,kBAAmB,CAFnB,aAAc,CAKd,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,YAAa,CAQb,uBAAyB,CATzB,UAUF,CAEA,iCAEE,gCAAgD,CADhD,0BAEF,CAEA,qBAEE,kBAAmB,CADnB,UAEF,CAEA,gBAOE,kCAAmC,CACnC,0BAA2B,CAJ3B,gBAAoC,CAEpC,kBAAmB,CAHnB,eAAgB,CAFhB,eAAgB,CAIhB,YAAa,CAHb,iBAOF,CAGA,sBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,uBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,yBACE,SACE,cACF,CAEA,iBAEE,QAAS,CADT,yBAEF,CAEA,iBACE,gBACF,CAEA,cACE,cACF,CAEA,cACE,sBACF,CACF", "sources": ["index.css", "App.css", "components/LoadingScreen.css", "components/ScrollProgress.css", "components/CursorFollower.css", "components/AccessibilityHelper.css", "components/Header.css", "components/DarkModeToggle.css", "components/Hero.css", "components/About.css", "components/Skills.css", "components/Experience.css", "components/Certificates.css", "components/Projects.css", "components/Contact.css"], "sourcesContent": ["/* Import Clean Modern Fonts */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap');\n\nbody {\n  margin: 0;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overflow-x: hidden;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n}\n\n/* Selection styles */\n::selection {\n  background: rgba(102, 126, 234, 0.3);\n  color: #2d3748;\n}\n\n::-moz-selection {\n  background: rgba(102, 126, 234, 0.3);\n  color: #2d3748;\n}\n", "/* Clean Modern Design System */\n:root {\n  /* Clean Color Palette */\n  --primary: #0066ff;\n  --primary-light: #3385ff;\n  --primary-dark: #0052cc;\n  --secondary: #6366f1;\n  --accent: #10b981;\n  --warning: #f59e0b;\n  --error: #ef4444;\n\n  /* Neutral Colors */\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n\n  /* Text Colors */\n  --text-primary: var(--gray-900);\n  --text-secondary: var(--gray-600);\n  --text-muted: var(--gray-500);\n  --text-inverse: #ffffff;\n\n  /* Background Colors */\n  --bg-primary: #ffffff;\n  --bg-secondary: var(--gray-50);\n  --bg-tertiary: var(--gray-100);\n\n  /* Clean Shadows */\n  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n\n  /* Border Radius */\n  --radius-xs: 0.125rem;\n  --radius-sm: 0.25rem;\n  --radius-md: 0.375rem;\n  --radius-lg: 0.5rem;\n  --radius-xl: 0.75rem;\n  --radius-2xl: 1rem;\n  --radius-3xl: 1.5rem;\n  --radius-full: 9999px;\n\n  /* Spacing Scale */\n  --space-1: 0.25rem;\n  --space-2: 0.5rem;\n  --space-3: 0.75rem;\n  --space-4: 1rem;\n  --space-5: 1.25rem;\n  --space-6: 1.5rem;\n  --space-8: 2rem;\n  --space-10: 2.5rem;\n  --space-12: 3rem;\n  --space-16: 4rem;\n  --space-20: 5rem;\n  --space-24: 6rem;\n  --space-32: 8rem;\n\n  /* Typography Scale */\n  --text-xs: 0.75rem;\n  --text-sm: 0.875rem;\n  --text-base: 1rem;\n  --text-lg: 1.125rem;\n  --text-xl: 1.25rem;\n  --text-2xl: 1.5rem;\n  --text-3xl: 1.875rem;\n  --text-4xl: 2.25rem;\n  --text-5xl: 3rem;\n  --text-6xl: 3.75rem;\n  --text-7xl: 4.5rem;\n\n  /* Animation Timing */\n  --duration-fast: 150ms;\n  --duration-normal: 250ms;\n  --duration-slow: 350ms;\n  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n  --ease-out: cubic-bezier(0, 0, 0.2, 1);\n  --ease-in: cubic-bezier(0.4, 0, 1, 1);\n}\n\n/* Reset and Base Styles */\n*,\n*::before,\n*::after {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n  font-size: 16px;\n  height: 100%;\n}\n\nbody {\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: var(--text-primary);\n  background-color: var(--bg-primary);\n  font-weight: 400;\n  letter-spacing: -0.011em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  height: 100%;\n}\n\n.App {\n  min-height: 100vh;\n  position: relative;\n  overflow-x: hidden;\n  background: var(--bg-primary);\n}\n\n/* Layout Components */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 0 var(--space-4);\n  }\n}\n\n/* Clean Section Titles */\n.section-title {\n  font-family: 'Space Grotesk', sans-serif;\n  font-size: clamp(var(--text-3xl), 5vw, var(--text-5xl));\n  font-weight: 600;\n  text-align: center;\n  margin-bottom: var(--space-16);\n  color: var(--text-primary);\n  letter-spacing: -0.025em;\n  line-height: 1.1;\n  position: relative;\n}\n\n.section-title::after {\n  content: '';\n  position: absolute;\n  bottom: -var(--space-3);\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60px;\n  height: 2px;\n  background: var(--primary);\n  border-radius: var(--radius-full);\n}\n\n/* Clean Modern Buttons */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-3) var(--space-6);\n  border: none;\n  border-radius: var(--radius-lg);\n  font-size: var(--text-base);\n  font-weight: 500;\n  font-family: inherit;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all var(--duration-normal) var(--ease-out);\n  text-align: center;\n  position: relative;\n  letter-spacing: -0.011em;\n  min-width: 140px;\n  white-space: nowrap;\n}\n\n.btn-primary {\n  background: var(--primary);\n  color: var(--text-inverse);\n  box-shadow: var(--shadow-sm);\n}\n\n.btn-primary:hover {\n  background: var(--primary-dark);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.btn-primary:active {\n  transform: translateY(0);\n  box-shadow: var(--shadow-xs);\n}\n\n.btn-secondary {\n  background: transparent;\n  color: var(--primary);\n  border: 1px solid var(--gray-300);\n  box-shadow: var(--shadow-xs);\n}\n\n.btn-secondary:hover {\n  background: var(--gray-50);\n  border-color: var(--primary);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-sm);\n}\n\n.btn-secondary:active {\n  transform: translateY(0);\n  background: var(--gray-100);\n}\n\n/* Utility Classes */\n.text-center { text-align: center; }\n.text-left { text-align: left; }\n.text-right { text-align: right; }\n\n.font-medium { font-weight: 500; }\n.font-semibold { font-weight: 600; }\n.font-bold { font-weight: 700; }\n\n.text-primary { color: var(--text-primary); }\n.text-secondary { color: var(--text-secondary); }\n.text-muted { color: var(--text-muted); }\n\n.bg-primary { background-color: var(--bg-primary); }\n.bg-secondary { background-color: var(--bg-secondary); }\n\n.rounded { border-radius: var(--radius-md); }\n.rounded-lg { border-radius: var(--radius-lg); }\n.rounded-xl { border-radius: var(--radius-xl); }\n.rounded-full { border-radius: var(--radius-full); }\n\n.shadow-sm { box-shadow: var(--shadow-sm); }\n.shadow-md { box-shadow: var(--shadow-md); }\n.shadow-lg { box-shadow: var(--shadow-lg); }\n\n/* Responsive Breakpoints */\n@media (max-width: 640px) {\n  .container {\n    padding: 0 var(--space-4);\n  }\n\n  .section-title {\n    margin-bottom: var(--space-12);\n  }\n\n  .btn {\n    padding: var(--space-3) var(--space-5);\n    font-size: var(--text-sm);\n    min-width: 120px;\n  }\n}\n\n/* Smooth Animations */\n@media (prefers-reduced-motion: no-preference) {\n  html {\n    scroll-behavior: smooth;\n  }\n}\n\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n    scroll-behavior: auto !important;\n  }\n}\n", ".loading-screen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n  transition: opacity 0.8s ease, visibility 0.8s ease;\n}\n\n.loading-screen.fade-out {\n  opacity: 0;\n  visibility: hidden;\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n  position: relative;\n  z-index: 2;\n}\n\n.loading-logo h1 {\n  font-family: 'Playfair Display', serif;\n  font-size: 4rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n  letter-spacing: -0.02em;\n  animation: logoGlow 2s ease-in-out infinite alternate;\n}\n\n.logo-underline {\n  width: 100px;\n  height: 4px;\n  background: linear-gradient(90deg, #ffd89b, #19547b);\n  margin: 0 auto 3rem;\n  border-radius: 2px;\n  animation: underlineExpand 2s ease-in-out infinite;\n}\n\n.loading-progress {\n  margin-bottom: 2rem;\n}\n\n.progress-bar {\n  width: 300px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  margin: 0 auto 1rem;\n  overflow: hidden;\n  position: relative;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #ffd89b, #19547b);\n  border-radius: 2px;\n  transition: width 0.3s ease;\n  position: relative;\n}\n\n.progress-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  animation: progressShimmer 1.5s ease-in-out infinite;\n}\n\n.progress-text {\n  font-family: 'JetBrains Mono', monospace;\n  font-size: 1.2rem;\n  font-weight: 600;\n  opacity: 0.9;\n}\n\n.loading-subtitle {\n  font-size: 1.1rem;\n  font-weight: 300;\n  opacity: 0.8;\n  letter-spacing: 0.1em;\n  text-transform: uppercase;\n  animation: subtitleFade 3s ease-in-out infinite;\n}\n\n.loading-particles {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  z-index: 1;\n}\n\n.particle {\n  position: absolute;\n  width: 4px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.6);\n  border-radius: 50%;\n  animation: particleFloat 8s linear infinite;\n}\n\n.particle-1 { left: 10%; animation-delay: 0s; }\n.particle-2 { left: 20%; animation-delay: 0.5s; }\n.particle-3 { left: 30%; animation-delay: 1s; }\n.particle-4 { left: 40%; animation-delay: 1.5s; }\n.particle-5 { left: 50%; animation-delay: 2s; }\n.particle-6 { left: 60%; animation-delay: 2.5s; }\n.particle-7 { left: 70%; animation-delay: 3s; }\n.particle-8 { left: 80%; animation-delay: 3.5s; }\n.particle-9 { left: 90%; animation-delay: 4s; }\n.particle-10 { left: 15%; animation-delay: 4.5s; }\n.particle-11 { left: 25%; animation-delay: 5s; }\n.particle-12 { left: 35%; animation-delay: 5.5s; }\n.particle-13 { left: 45%; animation-delay: 6s; }\n.particle-14 { left: 55%; animation-delay: 6.5s; }\n.particle-15 { left: 65%; animation-delay: 7s; }\n.particle-16 { left: 75%; animation-delay: 7.5s; }\n.particle-17 { left: 85%; animation-delay: 8s; }\n.particle-18 { left: 95%; animation-delay: 8.5s; }\n.particle-19 { left: 5%; animation-delay: 9s; }\n.particle-20 { left: 95%; animation-delay: 9.5s; }\n\n/* Animations */\n@keyframes logoGlow {\n  0% {\n    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);\n    transform: scale(1);\n  }\n  100% {\n    text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);\n    transform: scale(1.02);\n  }\n}\n\n@keyframes underlineExpand {\n  0%, 100% {\n    transform: scaleX(1);\n  }\n  50% {\n    transform: scaleX(1.2);\n  }\n}\n\n@keyframes progressShimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n@keyframes subtitleFade {\n  0%, 100% {\n    opacity: 0.8;\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n@keyframes particleFloat {\n  0% {\n    transform: translateY(100vh) rotate(0deg);\n    opacity: 0;\n  }\n  10% {\n    opacity: 1;\n  }\n  90% {\n    opacity: 1;\n  }\n  100% {\n    transform: translateY(-100px) rotate(360deg);\n    opacity: 0;\n  }\n}\n\n/* Mobile Styles */\n@media (max-width: 768px) {\n  .loading-logo h1 {\n    font-size: 3rem;\n  }\n  \n  .progress-bar {\n    width: 250px;\n  }\n  \n  .loading-subtitle {\n    font-size: 1rem;\n  }\n}\n", ".scroll-progress-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.1);\n  z-index: 1001;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n\n.scroll-progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  transition: width 0.1s ease;\n  position: relative;\n}\n\n.scroll-progress-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 20px;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6));\n  animation: progressGlow 2s ease-in-out infinite;\n}\n\n.scroll-to-top {\n  position: fixed;\n  bottom: 2rem;\n  right: 2rem;\n  width: 60px;\n  height: 60px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 50%;\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  transform: translateY(100px);\n  opacity: 0;\n  visibility: hidden;\n  z-index: 1000;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n\n.scroll-to-top.visible {\n  transform: translateY(0);\n  opacity: 1;\n  visibility: visible;\n}\n\n.scroll-to-top:hover {\n  transform: translateY(-5px) scale(1.1);\n  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);\n}\n\n.scroll-to-top:active {\n  transform: translateY(-2px) scale(1.05);\n}\n\n.scroll-progress-ring {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.scroll-progress-ring svg {\n  width: 100%;\n  height: 100%;\n  transform: rotate(-90deg);\n}\n\n.scroll-to-top svg {\n  width: 24px;\n  height: 24px;\n  z-index: 1;\n  position: relative;\n}\n\n/* Animations */\n@keyframes progressGlow {\n  0%, 100% {\n    opacity: 0.6;\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n/* Mobile Styles */\n@media (max-width: 768px) {\n  .scroll-to-top {\n    bottom: 1.5rem;\n    right: 1.5rem;\n    width: 50px;\n    height: 50px;\n  }\n  \n  .scroll-to-top svg {\n    width: 20px;\n    height: 20px;\n  }\n  \n  .scroll-progress-ring svg {\n    width: 50px;\n    height: 50px;\n  }\n}\n", "/* Hide default cursor on desktop */\n@media (min-width: 769px) {\n  * {\n    cursor: none !important;\n  }\n}\n\n.cursor-follower {\n  position: fixed;\n  width: 40px;\n  height: 40px;\n  pointer-events: none;\n  z-index: 9998;\n  transform: translate(-50%, -50%);\n  transition: all 0.1s ease;\n  mix-blend-mode: difference;\n}\n\n.cursor-inner {\n  width: 100%;\n  height: 100%;\n  border: 2px solid white;\n  border-radius: 50%;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n}\n\n.cursor-inner::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 8px;\n  height: 8px;\n  background: white;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  transition: all 0.3s ease;\n}\n\n.cursor-trail {\n  position: fixed;\n  width: 8px;\n  height: 8px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  pointer-events: none;\n  z-index: 9997;\n  transform: translate(-50%, -50%);\n  transition: all 0.15s ease;\n  opacity: 0.8;\n}\n\n/* Hover state */\n.cursor-follower.hovering {\n  transform: translate(-50%, -50%) scale(1.5);\n}\n\n.cursor-follower.hovering .cursor-inner {\n  border-color: #667eea;\n  background: rgba(102, 126, 234, 0.1);\n}\n\n.cursor-follower.hovering .cursor-inner::before {\n  background: #667eea;\n  transform: translate(-50%, -50%) scale(1.5);\n}\n\n/* Clicking state */\n.cursor-follower.clicking {\n  transform: translate(-50%, -50%) scale(0.8);\n}\n\n.cursor-follower.clicking .cursor-inner {\n  border-color: #764ba2;\n  background: rgba(118, 75, 162, 0.2);\n}\n\n.cursor-follower.clicking .cursor-inner::before {\n  background: #764ba2;\n  transform: translate(-50%, -50%) scale(2);\n}\n\n/* Pulse animation for interactive elements */\n.cursor-follower.hovering .cursor-inner::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 100%;\n  height: 100%;\n  border: 2px solid #667eea;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  animation: cursorPulse 1.5s ease-in-out infinite;\n}\n\n@keyframes cursorPulse {\n  0% {\n    transform: translate(-50%, -50%) scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: translate(-50%, -50%) scale(2);\n    opacity: 0;\n  }\n}\n\n/* Mobile - hide cursor follower */\n@media (max-width: 768px) {\n  .cursor-follower,\n  .cursor-trail {\n    display: none;\n  }\n  \n  * {\n    cursor: auto !important;\n  }\n}\n", ".accessibility-toggle {\n  position: fixed;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n  width: 50px;\n  height: 50px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 0 25px 25px 0;\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);\n  transition: all 0.3s ease;\n  z-index: 1001;\n}\n\n.accessibility-toggle:hover {\n  transform: translateY(-50%) translateX(5px);\n  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);\n}\n\n.accessibility-toggle:focus {\n  outline: 3px solid #ffd89b;\n  outline-offset: 2px;\n}\n\n.accessibility-panel {\n  position: fixed;\n  top: 0;\n  left: -350px;\n  width: 350px;\n  height: 100vh;\n  background: white;\n  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);\n  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  z-index: 1002;\n  overflow-y: auto;\n}\n\n.accessibility-panel.open {\n  left: 0;\n}\n\n.accessibility-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.accessibility-header h3 {\n  margin: 0;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: background 0.2s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.close-btn:focus {\n  outline: 2px solid white;\n  outline-offset: 2px;\n}\n\n.accessibility-content {\n  padding: 1.5rem;\n}\n\n.accessibility-group {\n  margin-bottom: 2rem;\n}\n\n.accessibility-group h4 {\n  margin: 0 0 1rem 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #2d3748;\n}\n\n.font-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.font-controls button {\n  padding: 0.5rem 1rem;\n  border: 2px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  border-radius: 6px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.2s ease;\n}\n\n.font-controls button:hover:not(:disabled) {\n  border-color: #667eea;\n  color: #667eea;\n}\n\n.font-controls button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.font-controls button:focus {\n  outline: 2px solid #667eea;\n  outline-offset: 2px;\n}\n\n.font-size-display {\n  padding: 0.5rem 1rem;\n  background: #f7fafc;\n  border-radius: 6px;\n  font-weight: 600;\n  color: #2d3748;\n  min-width: 60px;\n  text-align: center;\n}\n\n.accessibility-checkbox {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #2d3748;\n}\n\n.accessibility-checkbox input {\n  position: absolute;\n  opacity: 0;\n  cursor: pointer;\n}\n\n.checkmark {\n  width: 20px;\n  height: 20px;\n  background: white;\n  border: 2px solid #e2e8f0;\n  border-radius: 4px;\n  margin-right: 0.75rem;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.accessibility-checkbox input:checked + .checkmark {\n  background: #667eea;\n  border-color: #667eea;\n}\n\n.accessibility-checkbox input:checked + .checkmark::after {\n  content: '';\n  position: absolute;\n  left: 6px;\n  top: 2px;\n  width: 6px;\n  height: 10px;\n  border: solid white;\n  border-width: 0 2px 2px 0;\n  transform: rotate(45deg);\n}\n\n.accessibility-checkbox input:focus + .checkmark {\n  outline: 2px solid #667eea;\n  outline-offset: 2px;\n}\n\n.option-description {\n  margin: 0;\n  font-size: 0.875rem;\n  color: #718096;\n  margin-left: 2.75rem;\n  line-height: 1.4;\n}\n\n.accessibility-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 1001;\n}\n\n/* High contrast mode styles */\n:root.high-contrast {\n  --primary-gradient: linear-gradient(135deg, #000000 0%, #333333 100%);\n  --text-primary: #000000;\n  --text-secondary: #000000;\n  --bg-primary: #ffffff;\n  --bg-secondary: #ffffff;\n}\n\n:root.high-contrast .hero {\n  background: #000000 !important;\n  color: #ffffff !important;\n}\n\n:root.high-contrast .hero-title,\n:root.high-contrast .hero-subtitle,\n:root.high-contrast .hero-description {\n  color: #ffffff !important;\n}\n\n/* Reduced motion styles */\n:root.reduced-motion * {\n  animation-duration: 0.01ms !important;\n  animation-iteration-count: 1 !important;\n  transition-duration: 0.01ms !important;\n  scroll-behavior: auto !important;\n}\n\n/* Mobile styles */\n@media (max-width: 768px) {\n  .accessibility-panel {\n    width: 100%;\n    left: -100%;\n  }\n  \n  .accessibility-toggle {\n    width: 45px;\n    height: 45px;\n  }\n}\n", ".header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n  z-index: 1000;\n  transition: all var(--duration-normal) var(--ease-out);\n  border-bottom: 1px solid var(--gray-200);\n}\n\n.header.scrolled {\n  background: rgba(255, 255, 255, 0.98);\n  box-shadow: var(--shadow-sm);\n  border-bottom: 1px solid var(--gray-300);\n}\n\n.header-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 72px;\n}\n\n.logo h2 {\n  font-family: 'Space Grotesk', sans-serif;\n  font-size: var(--text-xl);\n  font-weight: 700;\n  color: var(--text-primary);\n  letter-spacing: -0.025em;\n  position: relative;\n  transition: color var(--duration-normal) var(--ease-out);\n}\n\n.logo:hover h2 {\n  color: var(--primary);\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n}\n\n.nav ul {\n  display: flex;\n  list-style: none;\n  gap: var(--space-8);\n}\n\n.nav button {\n  background: none;\n  border: none;\n  color: var(--text-secondary);\n  font-size: var(--text-sm);\n  font-weight: 500;\n  cursor: pointer;\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-md);\n  position: relative;\n  transition: all var(--duration-normal) var(--ease-out);\n  letter-spacing: -0.011em;\n}\n\n.nav button:hover {\n  color: var(--text-primary);\n  background: var(--gray-100);\n}\n\n.nav button:focus {\n  outline: 2px solid var(--primary);\n  outline-offset: 2px;\n}\n\n.mobile-menu-toggle {\n  display: none;\n  flex-direction: column;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: var(--space-2);\n  border-radius: var(--radius-md);\n  transition: background var(--duration-normal) var(--ease-out);\n}\n\n.mobile-menu-toggle:hover {\n  background: var(--gray-100);\n}\n\n.mobile-menu-toggle span {\n  width: 24px;\n  height: 2px;\n  background: var(--text-secondary);\n  margin: 3px 0;\n  transition: all var(--duration-normal) var(--ease-out);\n  border-radius: var(--radius-full);\n}\n\n/* Mobile Responsive */\n@media (max-width: 768px) {\n  .header-container {\n    padding: 0 var(--space-4);\n  }\n\n  .mobile-menu-toggle {\n    display: flex;\n  }\n\n  .nav {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    right: 0;\n    background: var(--bg-primary);\n    border-top: 1px solid var(--gray-200);\n    box-shadow: var(--shadow-lg);\n    transform: translateY(-100%);\n    opacity: 0;\n    visibility: hidden;\n    transition: all var(--duration-normal) var(--ease-out);\n  }\n\n  .nav.nav-open {\n    transform: translateY(0);\n    opacity: 1;\n    visibility: visible;\n  }\n\n  .nav ul {\n    flex-direction: column;\n    padding: var(--space-4) 0;\n    gap: 0;\n  }\n\n  .nav button {\n    padding: var(--space-4) var(--space-6);\n    text-align: left;\n    width: 100%;\n    border-radius: 0;\n    font-size: var(--text-base);\n  }\n\n  .nav button:hover {\n    background: var(--gray-50);\n  }\n\n  .header-actions {\n    gap: var(--space-2);\n  }\n\n  .mobile-menu-toggle.active span:nth-child(1) {\n    transform: rotate(-45deg) translate(-5px, 6px);\n  }\n\n  .mobile-menu-toggle.active span:nth-child(2) {\n    opacity: 0;\n  }\n\n  .mobile-menu-toggle.active span:nth-child(3) {\n    transform: rotate(45deg) translate(-5px, -6px);\n  }\n}\n", ".dark-mode-toggle {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 2rem;\n  transition: all 0.3s ease;\n  color: var(--text-primary);\n}\n\n.dark-mode-toggle:hover {\n  background: rgba(102, 126, 234, 0.1);\n}\n\n.dark-mode-toggle:focus {\n  outline: 2px solid #667eea;\n  outline-offset: 2px;\n}\n\n.toggle-track {\n  width: 50px;\n  height: 26px;\n  background: #e2e8f0;\n  border-radius: 13px;\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.dark-mode-toggle.dark .toggle-track {\n  background: #4a5568;\n}\n\n.toggle-thumb {\n  width: 22px;\n  height: 22px;\n  background: white;\n  border-radius: 50%;\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.dark-mode-toggle.dark .toggle-thumb {\n  transform: translateX(24px);\n  background: #2d3748;\n}\n\n.toggle-icon {\n  color: #fbbf24;\n  transition: all 0.3s ease;\n}\n\n.dark-mode-toggle.dark .toggle-icon {\n  color: #f7fafc;\n}\n\n.toggle-label {\n  font-size: 0.875rem;\n  font-weight: 500;\n  -webkit-user-select: none;\n  user-select: none;\n}\n\n/* Dark mode global styles */\n:root.dark-mode {\n  --text-primary: #f7fafc;\n  --text-secondary: #e2e8f0;\n  --text-muted: #a0aec0;\n  --text-light: #718096;\n  \n  --bg-primary: #1a202c;\n  --bg-secondary: #2d3748;\n  --bg-tertiary: #4a5568;\n  \n  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n  --dark-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n:root.dark-mode body {\n  background-color: var(--bg-primary);\n  color: var(--text-primary);\n}\n\n/* Dark mode component overrides */\n:root.dark-mode .header {\n  background: rgba(26, 32, 44, 0.95);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n:root.dark-mode .header.scrolled {\n  background: rgba(26, 32, 44, 0.98);\n  box-shadow: \n    0 4px 32px rgba(0, 0, 0, 0.3),\n    0 2px 16px rgba(0, 0, 0, 0.2);\n}\n\n:root.dark-mode .hero {\n  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);\n}\n\n:root.dark-mode .about {\n  background: var(--bg-primary);\n}\n\n:root.dark-mode .skills {\n  background: var(--bg-secondary);\n}\n\n:root.dark-mode .experience {\n  background: var(--bg-primary);\n}\n\n:root.dark-mode .certificates {\n  background: var(--bg-secondary);\n}\n\n:root.dark-mode .projects {\n  background: var(--bg-primary);\n}\n\n:root.dark-mode .contact {\n  background: var(--primary-gradient);\n}\n\n/* Dark mode card styles */\n:root.dark-mode .skill-category,\n:root.dark-mode .experience-content,\n:root.dark-mode .certificate-card,\n:root.dark-mode .project-card {\n  background: var(--bg-tertiary);\n  color: var(--text-primary);\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n}\n\n:root.dark-mode .skill-category:hover,\n:root.dark-mode .experience-content:hover,\n:root.dark-mode .certificate-card:hover,\n:root.dark-mode .project-card:hover {\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);\n}\n\n/* Dark mode form styles */\n:root.dark-mode .contact-form {\n  background: rgba(45, 55, 72, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n:root.dark-mode .form-group input,\n:root.dark-mode .form-group textarea {\n  background: rgba(45, 55, 72, 0.5);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: var(--text-primary);\n}\n\n:root.dark-mode .form-group input:focus,\n:root.dark-mode .form-group textarea:focus {\n  border-color: rgba(255, 255, 255, 0.4);\n  background: rgba(45, 55, 72, 0.7);\n}\n\n/* Dark mode accessibility panel */\n:root.dark-mode .accessibility-panel {\n  background: var(--bg-secondary);\n  color: var(--text-primary);\n}\n\n:root.dark-mode .accessibility-group h4 {\n  color: var(--text-primary);\n}\n\n:root.dark-mode .font-controls button {\n  background: var(--bg-tertiary);\n  border-color: rgba(255, 255, 255, 0.2);\n  color: var(--text-primary);\n}\n\n:root.dark-mode .font-size-display {\n  background: var(--bg-tertiary);\n  color: var(--text-primary);\n}\n\n:root.dark-mode .checkmark {\n  background: var(--bg-tertiary);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n\n/* Dark mode loading screen */\n:root.dark-mode .loading-screen {\n  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);\n}\n\n/* Smooth transitions for theme switching */\n* {\n  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;\n}\n\n/* Mobile styles */\n@media (max-width: 768px) {\n  .dark-mode-toggle {\n    gap: 0.5rem;\n  }\n  \n  .toggle-track {\n    width: 44px;\n    height: 24px;\n  }\n  \n  .toggle-thumb {\n    width: 20px;\n    height: 20px;\n  }\n  \n  .dark-mode-toggle.dark .toggle-thumb {\n    transform: translateX(20px);\n  }\n  \n  .toggle-label {\n    font-size: 0.8rem;\n  }\n}\n", ".hero {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n}\n\n.hero::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 50%;\n  height: 100%;\n  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);\n  clip-path: polygon(20% 0%, 100% 0%, 100% 100%, 0% 100%);\n  z-index: 1;\n}\n\n.hero-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-16);\n  align-items: center;\n  position: relative;\n  z-index: 2;\n  min-height: 100vh;\n}\n\n.hero-content {\n  opacity: 0;\n  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;\n}\n\n.hero-title {\n  font-family: 'Space Grotesk', sans-serif;\n  font-size: clamp(var(--text-4xl), 8vw, var(--text-6xl));\n  font-weight: 700;\n  line-height: 1.1;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  letter-spacing: -0.025em;\n}\n\n.highlight {\n  color: var(--primary);\n  position: relative;\n}\n\n.hero-subtitle {\n  font-size: clamp(var(--text-lg), 3vw, var(--text-2xl));\n  font-weight: 500;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-6);\n  letter-spacing: -0.011em;\n}\n\n.hero-description {\n  font-size: clamp(var(--text-base), 2vw, var(--text-lg));\n  color: var(--text-muted);\n  margin-bottom: var(--space-8);\n  line-height: 1.7;\n  max-width: 500px;\n}\n\n.hero-buttons {\n  display: flex;\n  gap: var(--space-4);\n  flex-wrap: wrap;\n}\n\n.hero-image {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  opacity: 0;\n  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;\n}\n\n.hero-avatar {\n  position: relative;\n}\n\n.avatar-placeholder {\n  width: 320px;\n  height: 320px;\n  border-radius: var(--radius-3xl);\n  background: var(--gray-100);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 8rem;\n  box-shadow: var(--shadow-xl);\n  position: relative;\n  overflow: hidden;\n  border: 1px solid var(--gray-200);\n}\n\n.avatar-placeholder::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\n  opacity: 0.1;\n}\n\n.hero-scroll-indicator {\n  position: absolute;\n  bottom: var(--space-8);\n  left: 50%;\n  transform: translateX(-50%);\n  z-index: 3;\n}\n\n.scroll-arrow {\n  cursor: pointer;\n  font-size: var(--text-2xl);\n  color: var(--text-muted);\n  animation: bounce 2s infinite;\n  transition: color var(--duration-normal) var(--ease-out);\n  padding: var(--space-2);\n  border-radius: var(--radius-full);\n  background: var(--bg-primary);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--gray-200);\n}\n\n.scroll-arrow:hover {\n  color: var(--primary);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-md);\n}\n\n/* Clean Modern Animations */\n@keyframes slideInLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-8px);\n  }\n  60% {\n    transform: translateY(-4px);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .hero::before {\n    display: none;\n  }\n\n  .hero-container {\n    grid-template-columns: 1fr;\n    gap: var(--space-12);\n    text-align: center;\n    padding: var(--space-4);\n  }\n\n  .hero-content {\n    order: 2;\n  }\n\n  .hero-image {\n    order: 1;\n  }\n\n  .avatar-placeholder {\n    width: 240px;\n    height: 240px;\n    font-size: 6rem;\n  }\n\n  .hero-buttons {\n    justify-content: center;\n    gap: var(--space-3);\n  }\n\n  .hero-buttons .btn {\n    flex: 1;\n    min-width: auto;\n  }\n}\n\n@media (max-width: 480px) {\n  .hero-container {\n    gap: var(--space-8);\n  }\n\n  .avatar-placeholder {\n    width: 200px;\n    height: 200px;\n    font-size: 5rem;\n  }\n\n  .hero-buttons {\n    flex-direction: column;\n  }\n}\n", ".about {\n  padding: var(--space-24) 0;\n  background: var(--bg-primary);\n}\n\n.about-content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-16);\n  align-items: center;\n}\n\n.about-text {\n  opacity: 0;\n  animation: slideInLeft 0.8s var(--ease-out) 0.2s forwards;\n}\n\n.about-text p {\n  font-size: var(--text-lg);\n  color: var(--text-secondary);\n  margin-bottom: var(--space-6);\n  line-height: 1.7;\n}\n\n.about-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: var(--space-6);\n  margin-top: var(--space-8);\n}\n\n.stat {\n  text-align: center;\n  padding: var(--space-6);\n  background: var(--bg-secondary);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--gray-200);\n  transition: all var(--duration-normal) var(--ease-out);\n}\n\n.stat:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--gray-300);\n}\n\n.stat h3 {\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--primary);\n  margin-bottom: var(--space-2);\n  font-family: 'Space Grotesk', sans-serif;\n}\n\n.stat p {\n  font-size: var(--text-sm);\n  color: var(--text-muted);\n  font-weight: 500;\n  margin: 0;\n}\n\n.about-image {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  opacity: 0;\n  animation: slideInRight 0.8s var(--ease-out) 0.4s forwards;\n}\n\n.image-placeholder {\n  width: 320px;\n  height: 380px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 2px dashed var(--gray-300);\n  transition: all var(--duration-normal) var(--ease-out);\n}\n\n.image-placeholder:hover {\n  border-color: var(--primary);\n  background: var(--gray-100);\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-lg);\n}\n\n.image-placeholder span {\n  font-size: var(--text-6xl);\n  margin-bottom: var(--space-4);\n}\n\n.image-placeholder p {\n  color: var(--text-muted);\n  font-weight: 500;\n  margin: 0;\n  font-size: var(--text-sm);\n}\n\n/* Clean Animations */\n@keyframes slideInLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .about {\n    padding: var(--space-20) 0;\n  }\n\n  .about-content {\n    grid-template-columns: 1fr;\n    gap: var(--space-12);\n  }\n\n  .about-stats {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--space-4);\n  }\n\n  .stat {\n    padding: var(--space-4);\n  }\n\n  .stat h3 {\n    font-size: var(--text-2xl);\n  }\n\n  .image-placeholder {\n    width: 280px;\n    height: 320px;\n  }\n\n  .image-placeholder span {\n    font-size: var(--text-5xl);\n  }\n}\n\n@media (max-width: 480px) {\n  .about-stats {\n    grid-template-columns: 1fr;\n  }\n\n  .image-placeholder {\n    width: 240px;\n    height: 280px;\n  }\n}\n", ".skills {\n  padding: var(--space-24) 0;\n  background: var(--bg-secondary);\n}\n\n.skills-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: var(--space-8);\n}\n\n.skill-category {\n  background: var(--bg-primary);\n  padding: var(--space-8);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  transition: all var(--duration-normal) var(--ease-out);\n  opacity: 0;\n  animation: slideInUp 0.8s var(--ease-out) forwards;\n}\n\n.skill-category:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--gray-300);\n}\n\n.category-title {\n  font-family: 'Space Grotesk', sans-serif;\n  font-size: var(--text-xl);\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  text-align: center;\n  position: relative;\n}\n\n.category-title::after {\n  content: '';\n  position: absolute;\n  bottom: -var(--space-2);\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40px;\n  height: 2px;\n  background: var(--primary);\n  border-radius: var(--radius-full);\n}\n\n.skills-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-5);\n}\n\n.skill-item {\n  position: relative;\n}\n\n.skill-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--space-2);\n}\n\n.skill-name {\n  font-weight: 500;\n  color: var(--text-secondary);\n  font-size: var(--text-sm);\n}\n\n.skill-percentage {\n  font-weight: 600;\n  color: var(--primary);\n  font-size: var(--text-xs);\n  font-family: 'JetBrains Mono', monospace;\n}\n\n.skill-bar {\n  height: 6px;\n  background: var(--gray-200);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  position: relative;\n}\n\n.skill-progress {\n  height: 100%;\n  background: var(--primary);\n  border-radius: var(--radius-full);\n  transition: width 1.5s var(--ease-out);\n  position: relative;\n}\n\n.skill-progress::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  animation: shimmer 2s infinite;\n}\n\n/* Clean Animations */\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n/* Stagger animation for skill categories */\n.skill-category:nth-child(1) {\n  animation-delay: 0s;\n}\n\n.skill-category:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.skill-category:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n.skill-category:nth-child(4) {\n  animation-delay: 0.6s;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .skills {\n    padding: var(--space-20) 0;\n  }\n\n  .skills-content {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n\n  .skill-category {\n    padding: var(--space-6);\n  }\n\n  .category-title {\n    font-size: var(--text-lg);\n  }\n\n  .skill-name {\n    font-size: var(--text-xs);\n  }\n\n  .skill-percentage {\n    font-size: var(--text-xs);\n  }\n}\n", ".experience {\n  padding: 5rem 0;\n  background: #ffffff;\n}\n\n.experience-timeline {\n  position: relative;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.experience-timeline::before {\n  content: '';\n  position: absolute;\n  left: 50%;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  transform: translateX(-50%);\n}\n\n.experience-item {\n  position: relative;\n  margin-bottom: 3rem;\n  width: 50%;\n  animation: fadeInUp 1s ease-out;\n}\n\n.experience-item.left {\n  left: 0;\n  padding-right: 3rem;\n}\n\n.experience-item.right {\n  left: 50%;\n  padding-left: 3rem;\n}\n\n.experience-content {\n  background: white;\n  padding: 2rem;\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.experience-content:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.experience-item.left .experience-content::before {\n  content: '';\n  position: absolute;\n  right: -15px;\n  top: 30px;\n  width: 0;\n  height: 0;\n  border: 15px solid transparent;\n  border-left-color: white;\n}\n\n.experience-item.right .experience-content::before {\n  content: '';\n  position: absolute;\n  left: -15px;\n  top: 30px;\n  width: 0;\n  height: 0;\n  border: 15px solid transparent;\n  border-right-color: white;\n}\n\n.experience-header {\n  margin-bottom: 1.5rem;\n}\n\n.position {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n}\n\n.company-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.company {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #667eea;\n  margin: 0;\n}\n\n.location {\n  font-size: 0.9rem;\n  color: #718096;\n  font-weight: 500;\n}\n\n.duration-type {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.duration {\n  font-size: 0.9rem;\n  color: #4a5568;\n  font-weight: 600;\n}\n\n.type {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n\n.type-full-time {\n  background: #c6f6d5;\n  color: #22543d;\n}\n\n.type-part-time {\n  background: #fed7d7;\n  color: #742a2a;\n}\n\n.type-contract {\n  background: #feebc8;\n  color: #7b341e;\n}\n\n.type-internship {\n  background: #e6fffa;\n  color: #234e52;\n}\n\n.experience-description ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.experience-description li {\n  position: relative;\n  padding-left: 1.5rem;\n  margin-bottom: 0.75rem;\n  color: #4a5568;\n  line-height: 1.6;\n}\n\n.experience-description li::before {\n  content: '▸';\n  position: absolute;\n  left: 0;\n  color: #667eea;\n  font-weight: bold;\n}\n\n.experience-technologies {\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.experience-technologies h5 {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.75rem;\n}\n\n.tech-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n}\n\n.tech-tag {\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\n  color: #4a5568;\n  padding: 0.25rem 0.75rem;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  border: 1px solid #e2e8f0;\n}\n\n.experience-marker {\n  position: absolute;\n  top: 30px;\n  width: 20px;\n  height: 20px;\n}\n\n.experience-item.left .experience-marker {\n  right: -10px;\n}\n\n.experience-item.right .experience-marker {\n  left: -10px;\n}\n\n.marker-dot {\n  width: 20px;\n  height: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  border: 4px solid white;\n  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Stagger animation for experience items */\n.experience-item:nth-child(1) {\n  animation-delay: 0s;\n}\n\n.experience-item:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.experience-item:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n.experience-item:nth-child(4) {\n  animation-delay: 0.6s;\n}\n\n/* Mobile Styles */\n@media (max-width: 768px) {\n  .experience {\n    padding: 3rem 0;\n  }\n\n  .experience-timeline::before {\n    left: 20px;\n  }\n\n  .experience-item {\n    width: 100%;\n    left: 0 !important;\n    padding-left: 3rem !important;\n    padding-right: 1rem !important;\n  }\n\n  .experience-content::before {\n    display: none;\n  }\n\n  .experience-marker {\n    left: 10px !important;\n    right: auto !important;\n  }\n\n  .company-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .duration-type {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .position {\n    font-size: 1.3rem;\n  }\n\n  .company {\n    font-size: 1.1rem;\n  }\n}\n", ".certificates {\n  padding: 5rem 0;\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\n}\n\n.certificate-filters {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n  margin-bottom: 3rem;\n  flex-wrap: wrap;\n}\n\n.filter-btn {\n  padding: 0.75rem 1.5rem;\n  border: 2px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  border-radius: 50px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.filter-btn:hover {\n  border-color: #667eea;\n  color: #667eea;\n}\n\n.filter-btn.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-color: transparent;\n}\n\n.certificates-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 2rem;\n  margin-top: 2rem;\n}\n\n.certificate-card {\n  background: white;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  animation: fadeInUp 1s ease-out;\n  position: relative;\n}\n\n.certificate-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.certificate-header {\n  position: relative;\n  height: 150px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.certificate-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);\n  background-size: 20px 20px;\n}\n\n.certificate-image {\n  z-index: 1;\n  position: relative;\n}\n\n.cert-icon {\n  font-size: 3rem;\n  color: white;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n}\n\n.certificate-badge {\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  z-index: 2;\n}\n\n.category-badge {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.certificate-content {\n  padding: 2rem;\n}\n\n.certificate-name {\n  font-size: 1.4rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n  line-height: 1.3;\n}\n\n.certificate-issuer {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #667eea;\n  margin-bottom: 0.5rem;\n}\n\n.certificate-date {\n  font-size: 0.9rem;\n  color: #718096;\n  margin-bottom: 1rem;\n  font-weight: 500;\n}\n\n.certificate-description {\n  color: #4a5568;\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n  font-size: 0.95rem;\n}\n\n.certificate-skills h5 {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.75rem;\n}\n\n.skills-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.skill-tag {\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\n  color: #4a5568;\n  padding: 0.25rem 0.75rem;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  border: 1px solid #e2e8f0;\n}\n\n.certificate-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.credential-id {\n  font-size: 0.8rem;\n  color: #718096;\n}\n\n.credential-id strong {\n  color: #4a5568;\n}\n\n.verify-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 25px;\n  text-decoration: none;\n  font-size: 0.9rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.verify-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);\n}\n\n.no-certificates {\n  text-align: center;\n  padding: 3rem;\n  color: #718096;\n  font-size: 1.1rem;\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Stagger animation for certificate cards */\n.certificate-card:nth-child(1) {\n  animation-delay: 0s;\n}\n\n.certificate-card:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.certificate-card:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n.certificate-card:nth-child(4) {\n  animation-delay: 0.6s;\n}\n\n.certificate-card:nth-child(5) {\n  animation-delay: 0.8s;\n}\n\n.certificate-card:nth-child(6) {\n  animation-delay: 1s;\n}\n\n/* Mobile Styles */\n@media (max-width: 768px) {\n  .certificates {\n    padding: 3rem 0;\n  }\n\n  .certificates-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .certificate-content {\n    padding: 1.5rem;\n  }\n\n  .certificate-name {\n    font-size: 1.2rem;\n  }\n\n  .certificate-actions {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .verify-btn {\n    text-align: center;\n  }\n\n  .filter-btn {\n    padding: 0.5rem 1rem;\n    font-size: 0.9rem;\n  }\n}\n", ".projects {\n  padding: 5rem 0;\n  background: #ffffff;\n}\n\n.project-filters {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n  margin-bottom: 3rem;\n  flex-wrap: wrap;\n}\n\n.filter-btn {\n  padding: 0.75rem 1.5rem;\n  border: 2px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  border-radius: 50px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.filter-btn:hover {\n  border-color: #667eea;\n  color: #667eea;\n}\n\n.filter-btn.active {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-color: transparent;\n}\n\n.projects-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 2rem;\n  margin-top: 2rem;\n}\n\n.project-card {\n  background: white;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  animation: fadeInUp 1s ease-out;\n}\n\n.project-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.project-image {\n  height: 200px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.project-image::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);\n  background-size: 20px 20px;\n}\n\n.project-emoji {\n  font-size: 4rem;\n  z-index: 1;\n  position: relative;\n}\n\n.project-content {\n  padding: 2rem;\n}\n\n.project-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 1rem;\n}\n\n.project-description {\n  color: #4a5568;\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n}\n\n.project-technologies {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.tech-tag {\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\n  color: #4a5568;\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  border: 1px solid #e2e8f0;\n}\n\n.project-links {\n  display: flex;\n  gap: 1rem;\n}\n\n.project-link {\n  padding: 0.75rem 1.5rem;\n  border-radius: 50px;\n  text-decoration: none;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  text-align: center;\n  flex: 1;\n}\n\n.project-link:first-child {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.project-link:first-child:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);\n}\n\n.project-link:last-child {\n  background: transparent;\n  color: #667eea;\n  border: 2px solid #667eea;\n}\n\n.project-link:last-child:hover {\n  background: #667eea;\n  color: white;\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Stagger animation for project cards */\n.project-card:nth-child(1) {\n  animation-delay: 0s;\n}\n\n.project-card:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.project-card:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n.project-card:nth-child(4) {\n  animation-delay: 0.6s;\n}\n\n/* Mobile Styles */\n@media (max-width: 768px) {\n  .projects {\n    padding: 3rem 0;\n  }\n\n  .projects-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .project-content {\n    padding: 1.5rem;\n  }\n\n  .project-title {\n    font-size: 1.3rem;\n  }\n\n  .project-links {\n    flex-direction: column;\n  }\n\n  .filter-btn {\n    padding: 0.5rem 1rem;\n    font-size: 0.9rem;\n  }\n}\n", ".contact {\n  padding: 5rem 0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.contact .section-title {\n  color: white;\n}\n\n.contact .section-title::after {\n  background: white;\n}\n\n.contact-content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 4rem;\n  align-items: start;\n}\n\n.contact-info {\n  animation: fadeInLeft 1s ease-out;\n}\n\n.contact-info h3 {\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 1rem;\n}\n\n.contact-info > p {\n  font-size: 1.1rem;\n  line-height: 1.7;\n  margin-bottom: 2rem;\n  opacity: 0.9;\n}\n\n.contact-details {\n  margin-bottom: 2rem;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.contact-icon {\n  font-size: 1.5rem;\n  width: 50px;\n  height: 50px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n\n.contact-item h4 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n}\n\n.contact-item p {\n  opacity: 0.9;\n  margin: 0;\n}\n\n.social-links {\n  display: flex;\n  gap: 1rem;\n}\n\n.social-link {\n  width: 50px;\n  height: 50px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-decoration: none;\n  font-size: 1.5rem;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n\n.social-link:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-3px);\n}\n\n.contact-form {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 2rem;\n  border-radius: 20px;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  animation: fadeInRight 1s ease-out 0.3s both;\n}\n\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n.form-group input,\n.form-group textarea {\n  width: 100%;\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 10px;\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n\n.form-group input::placeholder,\n.form-group textarea::placeholder {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.form-group input:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: rgba(255, 255, 255, 0.6);\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.submit-btn {\n  width: 100%;\n  padding: 1rem;\n  background: white;\n  color: #667eea;\n  border: none;\n  border-radius: 50px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.submit-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);\n}\n\n.submit-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.submit-message {\n  margin-top: 1rem;\n  text-align: center;\n  font-weight: 600;\n  background: rgba(255, 255, 255, 0.2);\n  padding: 1rem;\n  border-radius: 10px;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n\n/* Animations */\n@keyframes fadeInLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes fadeInRight {\n  from {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Mobile Styles */\n@media (max-width: 768px) {\n  .contact {\n    padding: 3rem 0;\n  }\n\n  .contact-content {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .contact-info h3 {\n    font-size: 1.5rem;\n  }\n\n  .contact-form {\n    padding: 1.5rem;\n  }\n\n  .social-links {\n    justify-content: center;\n  }\n}\n"], "names": [], "sourceRoot": ""}