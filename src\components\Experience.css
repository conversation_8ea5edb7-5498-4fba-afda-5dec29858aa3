.experience {
  padding: 5rem 0;
  background: #ffffff;
}

.experience-timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.experience-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateX(-50%);
}

.experience-item {
  position: relative;
  margin-bottom: 3rem;
  width: 50%;
  animation: fadeInUp 1s ease-out;
}

.experience-item.left {
  left: 0;
  padding-right: 3rem;
}

.experience-item.right {
  left: 50%;
  padding-left: 3rem;
}

.experience-content {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.experience-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.experience-item.left .experience-content::before {
  content: '';
  position: absolute;
  right: -15px;
  top: 30px;
  width: 0;
  height: 0;
  border: 15px solid transparent;
  border-left-color: white;
}

.experience-item.right .experience-content::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 30px;
  width: 0;
  height: 0;
  border: 15px solid transparent;
  border-right-color: white;
}

.experience-header {
  margin-bottom: 1.5rem;
}

.position {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.company-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.company {
  font-size: 1.2rem;
  font-weight: 600;
  color: #667eea;
  margin: 0;
}

.location {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

.duration-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.duration {
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 600;
}

.type {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-full-time {
  background: #c6f6d5;
  color: #22543d;
}

.type-part-time {
  background: #fed7d7;
  color: #742a2a;
}

.type-contract {
  background: #feebc8;
  color: #7b341e;
}

.type-internship {
  background: #e6fffa;
  color: #234e52;
}

.experience-description ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.experience-description li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
  color: #4a5568;
  line-height: 1.6;
}

.experience-description li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: bold;
}

.experience-technologies {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.experience-technologies h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #e2e8f0;
}

.experience-marker {
  position: absolute;
  top: 30px;
  width: 20px;
  height: 20px;
}

.experience-item.left .experience-marker {
  right: -10px;
}

.experience-item.right .experience-marker {
  left: -10px;
}

.marker-dot {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for experience items */
.experience-item:nth-child(1) {
  animation-delay: 0s;
}

.experience-item:nth-child(2) {
  animation-delay: 0.2s;
}

.experience-item:nth-child(3) {
  animation-delay: 0.4s;
}

.experience-item:nth-child(4) {
  animation-delay: 0.6s;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .experience {
    padding: 3rem 0;
  }

  .experience-timeline::before {
    left: 20px;
  }

  .experience-item {
    width: 100%;
    left: 0 !important;
    padding-left: 3rem !important;
    padding-right: 1rem !important;
  }

  .experience-content::before {
    display: none;
  }

  .experience-marker {
    left: 10px !important;
    right: auto !important;
  }

  .company-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .duration-type {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .position {
    font-size: 1.3rem;
  }

  .company {
    font-size: 1.1rem;
  }
}
