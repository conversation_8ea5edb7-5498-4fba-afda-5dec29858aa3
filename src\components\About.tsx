import React from 'react';
import './About.css';
import { portfolioConfig } from '../config/portfolioConfig';

const About: React.FC = () => {
  return (
    <section id="about" className="about">
      <div className="container">
        <h2 className="section-title">About Me</h2>
        <div className="about-content">
          <div className="about-text">
            {portfolioConfig.about.description.map((paragraph, index) => (
              <p key={index}>
                {paragraph}
              </p>
            ))}
            <div className="about-stats">
              {portfolioConfig.about.stats.map((stat, index) => (
                <div key={index} className="stat">
                  <h3>{stat.number}</h3>
                  <p>{stat.label}</p>
                </div>
              ))}
            </div>
          </div>
          <div className="about-image">
            <div className="image-placeholder">
              <span>📸</span>
              <p>Your Photo Here</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
