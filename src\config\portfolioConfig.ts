// Portfolio Configuration
// Update this file with your personal information

export const portfolioConfig = {
  // Personal Information
  personal: {
    name: "<PERSON><PERSON>",
    title: "Full Stack Developer & UI/UX Designer",
    description: "I create beautiful, responsive web applications with modern technologies. Passionate about clean code, user experience, and innovative solutions.",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Your City, Country",
    avatar: "👨‍💻", // You can replace this with an image URL
  },

  // About Section
  about: {
    description: [
      "I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. I specialize in modern web technologies and love turning complex problems into simple, beautiful designs.",
      "When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community. I believe in continuous learning and staying up-to-date with the latest industry trends."
    ],
    stats: [
      { number: "50+", label: "Projects Completed" },
      { number: "3+", label: "Years Experience" },
      { number: "20+", label: "Happy Clients" }
    ]
  },

  // Skills Section
  skills: [
    { name: 'JavaScript', level: 90, category: 'Frontend' },
    { name: 'TypeScript', level: 85, category: 'Frontend' },
    { name: 'React', level: 90, category: 'Frontend' },
    { name: 'Vue.js', level: 75, category: 'Frontend' },
    { name: 'HTML/CSS', level: 95, category: 'Frontend' },
    { name: 'Node.js', level: 80, category: 'Backend' },
    { name: 'Python', level: 75, category: 'Backend' },
    { name: 'Express.js', level: 80, category: 'Backend' },
    { name: 'MongoDB', level: 70, category: 'Database' },
    { name: 'PostgreSQL', level: 75, category: 'Database' },
    { name: 'Git', level: 85, category: 'Tools' },
    { name: 'Docker', level: 70, category: 'Tools' },
  ],

  // Experience Section
  experience: [
    {
      id: 1,
      company: "Tech Solutions Inc.",
      position: "Senior Full Stack Developer",
      duration: "Jan 2022 - Present",
      location: "Remote",
      description: [
        "Led development of scalable web applications serving 100k+ users",
        "Architected microservices infrastructure reducing response time by 40%",
        "Mentored junior developers and conducted code reviews",
        "Implemented CI/CD pipelines improving deployment efficiency by 60%"
      ],
      technologies: ["React", "Node.js", "TypeScript", "AWS", "Docker", "MongoDB"],
      type: "full-time"
    },
    {
      id: 2,
      company: "Digital Innovations Ltd.",
      position: "Frontend Developer",
      duration: "Jun 2020 - Dec 2021",
      location: "New York, NY",
      description: [
        "Developed responsive web applications using React and Vue.js",
        "Collaborated with UX/UI designers to implement pixel-perfect designs",
        "Optimized application performance resulting in 30% faster load times",
        "Integrated RESTful APIs and GraphQL endpoints"
      ],
      technologies: ["React", "Vue.js", "JavaScript", "CSS3", "REST API", "GraphQL"],
      type: "full-time"
    },
    {
      id: 3,
      company: "StartupXYZ",
      position: "Web Developer Intern",
      duration: "Jan 2020 - May 2020",
      location: "San Francisco, CA",
      description: [
        "Built responsive landing pages and marketing websites",
        "Assisted in developing e-commerce platform features",
        "Participated in agile development processes and daily standups",
        "Learned modern web development best practices"
      ],
      technologies: ["HTML5", "CSS3", "JavaScript", "Bootstrap", "jQuery"],
      type: "internship"
    }
  ],

  // Certificates Section
  certificates: [
    {
      id: 1,
      name: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services",
      date: "March 2023",
      credentialId: "AWS-SAA-123456",
      verificationUrl: "https://aws.amazon.com/verification",
      description: "Validates expertise in designing distributed systems on AWS platform with focus on scalability, security, and cost optimization.",
      skills: ["AWS", "Cloud Architecture", "Security", "Scalability"],
      category: "Cloud",
      image: "☁️"
    },
    {
      id: 2,
      name: "React Developer Certification",
      issuer: "Meta (Facebook)",
      date: "January 2023",
      credentialId: "META-REACT-789012",
      verificationUrl: "https://developers.facebook.com/certification",
      description: "Demonstrates proficiency in React ecosystem including hooks, context, state management, and modern development practices.",
      skills: ["React", "JavaScript", "Hooks", "State Management"],
      category: "Frontend",
      image: "⚛️"
    },
    {
      id: 3,
      name: "Google Cloud Professional Developer",
      issuer: "Google Cloud",
      date: "November 2022",
      credentialId: "GCP-DEV-345678",
      verificationUrl: "https://cloud.google.com/certification",
      description: "Validates ability to design, build, and deploy applications on Google Cloud Platform using best practices.",
      skills: ["GCP", "Kubernetes", "Cloud Functions", "BigQuery"],
      category: "Cloud",
      image: "🌐"
    },
    {
      id: 4,
      name: "MongoDB Certified Developer",
      issuer: "MongoDB University",
      date: "September 2022",
      credentialId: "MONGO-DEV-901234",
      verificationUrl: "https://university.mongodb.com/certification",
      description: "Demonstrates expertise in MongoDB database design, querying, indexing, and application development.",
      skills: ["MongoDB", "NoSQL", "Database Design", "Aggregation"],
      category: "Database",
      image: "🍃"
    },
    {
      id: 5,
      name: "Certified Kubernetes Administrator",
      issuer: "Cloud Native Computing Foundation",
      date: "July 2022",
      credentialId: "CKA-567890",
      verificationUrl: "https://www.cncf.io/certification",
      description: "Validates skills in Kubernetes cluster administration, troubleshooting, and application lifecycle management.",
      skills: ["Kubernetes", "Docker", "Container Orchestration", "DevOps"],
      category: "DevOps",
      image: "🚢"
    },
    {
      id: 6,
      name: "Scrum Master Certification",
      issuer: "Scrum Alliance",
      date: "April 2022",
      credentialId: "CSM-234567",
      verificationUrl: "https://www.scrumalliance.org/certification",
      description: "Certified in Scrum framework, agile methodologies, and team facilitation for effective project management.",
      skills: ["Scrum", "Agile", "Project Management", "Team Leadership"],
      category: "Management",
      image: "🏃‍♂️"
    }
  ],

  // Projects Section
  projects: [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      image: '🛒',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Web App'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
      technologies: ['Vue.js', 'Express.js', 'Socket.io', 'PostgreSQL'],
      image: '📋',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Web App'
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A responsive weather dashboard that displays current weather and forecasts for multiple cities with beautiful data visualizations.',
      technologies: ['React', 'Chart.js', 'Weather API', 'CSS3'],
      image: '🌤️',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Frontend'
    },
    {
      id: 4,
      title: 'Mobile Banking App',
      description: 'A React Native mobile application for banking services with biometric authentication and real-time transaction tracking.',
      technologies: ['React Native', 'Firebase', 'Redux', 'TypeScript'],
      image: '💳',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Mobile'
    }
  ],

  // Social Links
  social: {
    linkedin: "https://linkedin.com/in/yourusername",
    github: "https://github.com/yourusername",
    twitter: "https://twitter.com/yourusername",
    instagram: "https://instagram.com/yourusername",
  },

  // Navigation
  navigation: [
    { name: 'Home', id: 'hero' },
    { name: 'About', id: 'about' },
    { name: 'Skills', id: 'skills' },
    { name: 'Experience', id: 'experience' },
    { name: 'Certificates', id: 'certificates' },
    { name: 'Projects', id: 'projects' },
    { name: 'Contact', id: 'contact' }
  ]
};

export default portfolioConfig;
