// Portfolio Configuration
// Update this file with your personal information

export const portfolioConfig = {
  // Personal Information
  personal: {
    name: "<PERSON><PERSON>",
    title: "Full Stack Developer & UI/UX Designer",
    description: "I create beautiful, responsive web applications with modern technologies. Passionate about clean code, user experience, and innovative solutions.",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Your City, Country",
    avatar: "👨‍💻", // You can replace this with an image URL
  },

  // About Section
  about: {
    description: [
      "I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. I specialize in modern web technologies and love turning complex problems into simple, beautiful designs.",
      "When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community. I believe in continuous learning and staying up-to-date with the latest industry trends."
    ],
    stats: [
      { number: "50+", label: "Projects Completed" },
      { number: "3+", label: "Years Experience" },
      { number: "20+", label: "Happy Clients" }
    ]
  },

  // Skills Section
  skills: [
    { name: 'JavaScript', level: 90, category: 'Frontend' },
    { name: 'TypeScript', level: 85, category: 'Frontend' },
    { name: 'React', level: 90, category: 'Frontend' },
    { name: 'Vue.js', level: 75, category: 'Frontend' },
    { name: 'HTML/CSS', level: 95, category: 'Frontend' },
    { name: 'Node.js', level: 80, category: 'Backend' },
    { name: 'Python', level: 75, category: 'Backend' },
    { name: 'Express.js', level: 80, category: 'Backend' },
    { name: 'MongoDB', level: 70, category: 'Database' },
    { name: 'PostgreSQL', level: 75, category: 'Database' },
    { name: 'Git', level: 85, category: 'Tools' },
    { name: 'Docker', level: 70, category: 'Tools' },
  ],

  // Projects Section
  projects: [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      image: '🛒',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Web App'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
      technologies: ['Vue.js', 'Express.js', 'Socket.io', 'PostgreSQL'],
      image: '📋',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Web App'
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A responsive weather dashboard that displays current weather and forecasts for multiple cities with beautiful data visualizations.',
      technologies: ['React', 'Chart.js', 'Weather API', 'CSS3'],
      image: '🌤️',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Frontend'
    },
    {
      id: 4,
      title: 'Mobile Banking App',
      description: 'A React Native mobile application for banking services with biometric authentication and real-time transaction tracking.',
      technologies: ['React Native', 'Firebase', 'Redux', 'TypeScript'],
      image: '💳',
      liveUrl: 'https://your-project-url.com',
      githubUrl: 'https://github.com/yourusername/project',
      category: 'Mobile'
    }
  ],

  // Social Links
  social: {
    linkedin: "https://linkedin.com/in/yourusername",
    github: "https://github.com/yourusername",
    twitter: "https://twitter.com/yourusername",
    instagram: "https://instagram.com/yourusername",
  },

  // Navigation
  navigation: [
    { name: 'Home', id: 'hero' },
    { name: 'About', id: 'about' },
    { name: 'Skills', id: 'skills' },
    { name: 'Projects', id: 'projects' },
    { name: 'Contact', id: 'contact' }
  ]
};

export default portfolioConfig;
