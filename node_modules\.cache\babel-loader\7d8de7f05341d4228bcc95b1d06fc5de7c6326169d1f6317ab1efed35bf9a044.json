{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./ScrollProgress.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ScrollProgress=()=>{const[scrollProgress,setScrollProgress]=useState(0);const[isVisible,setIsVisible]=useState(false);useEffect(()=>{const handleScroll=()=>{const totalHeight=document.documentElement.scrollHeight-window.innerHeight;const progress=window.scrollY/totalHeight*100;setScrollProgress(progress);setIsVisible(window.scrollY>100);};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);const scrollToTop=()=>{window.scrollTo({top:0,behavior:'smooth'});};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"scroll-progress-bar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"scroll-progress-fill\",style:{width:`${scrollProgress}%`}})}),/*#__PURE__*/_jsxs(\"button\",{className:`scroll-to-top ${isVisible?'visible':''}`,onClick:scrollToTop,\"aria-label\":\"Scroll to top\",type:\"button\",children:[/*#__PURE__*/_jsx(\"svg\",{width:\"24\",height:\"24\",viewBox:\"0 0 24 24\",fill:\"none\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 19V5M5 12L12 5L19 12\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"scroll-progress-ring\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"60\",height:\"60\",viewBox:\"0 0 60 60\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"30\",cy:\"30\",r:\"28\",fill:\"none\",stroke:\"rgba(255, 255, 255, 0.2)\",strokeWidth:\"2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"30\",cy:\"30\",r:\"28\",fill:\"none\",stroke:\"url(#gradient)\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeDasharray:`${2*Math.PI*28}`,strokeDashoffset:`${2*Math.PI*28*(1-scrollProgress/100)}`,transform:\"rotate(-90 30 30)\"}),/*#__PURE__*/_jsx(\"defs\",{children:/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"gradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"0%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"#667eea\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"#764ba2\"})]})})]})})]})]});};export default ScrollProgress;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ScrollProgress", "scrollProgress", "setScrollProgress", "isVisible", "setIsVisible", "handleScroll", "totalHeight", "document", "documentElement", "scrollHeight", "window", "innerHeight", "progress", "scrollY", "addEventListener", "removeEventListener", "scrollToTop", "scrollTo", "top", "behavior", "children", "className", "style", "width", "onClick", "type", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "PI", "strokeDashoffset", "transform", "id", "x1", "y1", "x2", "y2", "offset", "stopColor"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/ScrollProgress.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ScrollProgress.css';\n\nconst ScrollProgress: React.FC = () => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const progress = (window.scrollY / totalHeight) * 100;\n      setScrollProgress(progress);\n      setIsVisible(window.scrollY > 100);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <>\n      {/* Scroll Progress Bar */}\n      <div className=\"scroll-progress-bar\">\n        {/* Inline style required for dynamic scroll percentage */}\n        <div\n          className=\"scroll-progress-fill\"\n          style={{ width: `${scrollProgress}%` }}\n        ></div>\n      </div>\n\n      {/* Scroll to Top Button */}\n      <button\n        className={`scroll-to-top ${isVisible ? 'visible' : ''}`}\n        onClick={scrollToTop}\n        aria-label=\"Scroll to top\"\n        type=\"button\"\n      >\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n          <path \n            d=\"M12 19V5M5 12L12 5L19 12\" \n            stroke=\"currentColor\" \n            strokeWidth=\"2\" \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\"\n          />\n        </svg>\n        <div className=\"scroll-progress-ring\">\n          <svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\">\n            <circle\n              cx=\"30\"\n              cy=\"30\"\n              r=\"28\"\n              fill=\"none\"\n              stroke=\"rgba(255, 255, 255, 0.2)\"\n              strokeWidth=\"2\"\n            />\n            <circle\n              cx=\"30\"\n              cy=\"30\"\n              r=\"28\"\n              fill=\"none\"\n              stroke=\"url(#gradient)\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeDasharray={`${2 * Math.PI * 28}`}\n              strokeDashoffset={`${2 * Math.PI * 28 * (1 - scrollProgress / 100)}`}\n              transform=\"rotate(-90 30 30)\"\n            />\n            <defs>\n              <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                <stop offset=\"0%\" stopColor=\"#667eea\" />\n                <stop offset=\"100%\" stopColor=\"#764ba2\" />\n              </linearGradient>\n            </defs>\n          </svg>\n        </div>\n      </button>\n    </>\n  );\n};\n\nexport default ScrollProgress;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE9B,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGV,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAACW,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAEjDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAY,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,WAAW,CAAGC,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAGC,MAAM,CAACC,WAAW,CAC9E,KAAM,CAAAC,QAAQ,CAAIF,MAAM,CAACG,OAAO,CAAGP,WAAW,CAAI,GAAG,CACrDJ,iBAAiB,CAACU,QAAQ,CAAC,CAC3BR,YAAY,CAACM,MAAM,CAACG,OAAO,CAAG,GAAG,CAAC,CACpC,CAAC,CAEDH,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAET,YAAY,CAAC,CAC/C,MAAO,IAAMK,MAAM,CAACK,mBAAmB,CAAC,QAAQ,CAAEV,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,WAAW,CAAGA,CAAA,GAAM,CACxBN,MAAM,CAACO,QAAQ,CAAC,CACdC,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,QACZ,CAAC,CAAC,CACJ,CAAC,CAED,mBACEtB,KAAA,CAAAE,SAAA,EAAAqB,QAAA,eAEEzB,IAAA,QAAK0B,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAElCzB,IAAA,QACE0B,SAAS,CAAC,sBAAsB,CAChCC,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGtB,cAAc,GAAI,CAAE,CACnC,CAAC,CACJ,CAAC,cAGNJ,KAAA,WACEwB,SAAS,CAAE,iBAAiBlB,SAAS,CAAG,SAAS,CAAG,EAAE,EAAG,CACzDqB,OAAO,CAAER,WAAY,CACrB,aAAW,eAAe,CAC1BS,IAAI,CAAC,QAAQ,CAAAL,QAAA,eAEbzB,IAAA,QAAK4B,KAAK,CAAC,IAAI,CAACG,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAAAR,QAAA,cACzDzB,IAAA,SACEkC,CAAC,CAAC,0BAA0B,CAC5BC,MAAM,CAAC,cAAc,CACrBC,WAAW,CAAC,GAAG,CACfC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACvB,CAAC,CACC,CAAC,cACNtC,IAAA,QAAK0B,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnCvB,KAAA,QAAK0B,KAAK,CAAC,IAAI,CAACG,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,eAC7CzB,IAAA,WACEuC,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,IAAI,CACPC,CAAC,CAAC,IAAI,CACNR,IAAI,CAAC,MAAM,CACXE,MAAM,CAAC,0BAA0B,CACjCC,WAAW,CAAC,GAAG,CAChB,CAAC,cACFpC,IAAA,WACEuC,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,IAAI,CACPC,CAAC,CAAC,IAAI,CACNR,IAAI,CAAC,MAAM,CACXE,MAAM,CAAC,gBAAgB,CACvBC,WAAW,CAAC,GAAG,CACfC,aAAa,CAAC,OAAO,CACrBK,eAAe,CAAE,GAAG,CAAC,CAAGC,IAAI,CAACC,EAAE,CAAG,EAAE,EAAG,CACvCC,gBAAgB,CAAE,GAAG,CAAC,CAAGF,IAAI,CAACC,EAAE,CAAG,EAAE,EAAI,CAAC,CAAGtC,cAAc,CAAG,GAAG,CAAC,EAAG,CACrEwC,SAAS,CAAC,mBAAmB,CAC9B,CAAC,cACF9C,IAAA,SAAAyB,QAAA,cACEvB,KAAA,mBAAgB6C,EAAE,CAAC,UAAU,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,IAAI,CAAA1B,QAAA,eAC7DzB,IAAA,SAAMoD,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,SAAS,CAAE,CAAC,cACxCrD,IAAA,SAAMoD,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,SAAS,CAAE,CAAC,EAC5B,CAAC,CACb,CAAC,EACJ,CAAC,CACH,CAAC,EACA,CAAC,EACT,CAAC,CAEP,CAAC,CAED,cAAe,CAAAhD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}