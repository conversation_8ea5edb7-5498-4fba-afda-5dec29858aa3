{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\CursorFollower.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './CursorFollower.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CursorFollower = () => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const [isClicking, setIsClicking] = useState(false);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      setMousePosition({\n        x: e.clientX,\n        y: e.clientY\n      });\n    };\n    const handleMouseEnter = e => {\n      const target = e.target;\n      if (target.matches('button, a, .clickable, input, textarea')) {\n        setIsHovering(true);\n      }\n    };\n    const handleMouseLeave = e => {\n      const target = e.target;\n      if (target.matches('button, a, .clickable, input, textarea')) {\n        setIsHovering(false);\n      }\n    };\n    const handleMouseDown = () => {\n      setIsClicking(true);\n    };\n    const handleMouseUp = () => {\n      setIsClicking(false);\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseenter', handleMouseEnter, true);\n    document.addEventListener('mouseleave', handleMouseLeave, true);\n    document.addEventListener('mousedown', handleMouseDown);\n    document.addEventListener('mouseup', handleMouseUp);\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseenter', handleMouseEnter, true);\n      document.removeEventListener('mouseleave', handleMouseLeave, true);\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `cursor-follower ${isHovering ? 'hovering' : ''} ${isClicking ? 'clicking' : ''}`,\n      style: {\n        left: mousePosition.x,\n        top: mousePosition.y\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cursor-inner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cursor-trail\",\n      style: {\n        left: mousePosition.x,\n        top: mousePosition.y\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CursorFollower, \"VG9rUSDydV/gKpLZjQnsVrjcr68=\");\n_c = CursorFollower;\nexport default CursorFollower;\nvar _c;\n$RefreshReg$(_c, \"CursorFollower\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Cursor<PERSON><PERSON>ow<PERSON>", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "isClicking", "setIsClicking", "handleMouseMove", "e", "clientX", "clientY", "handleMouseEnter", "target", "matches", "handleMouseLeave", "handleMouseDown", "handleMouseUp", "document", "addEventListener", "removeEventListener", "children", "className", "style", "left", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/CursorFollower.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './CursorFollower.css';\n\nconst CursorFollower: React.FC = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const [isClicking, setIsClicking] = useState(false);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseEnter = (e: MouseEvent) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('button, a, .clickable, input, textarea')) {\n        setIsHovering(true);\n      }\n    };\n\n    const handleMouseLeave = (e: MouseEvent) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('button, a, .clickable, input, textarea')) {\n        setIsHovering(false);\n      }\n    };\n\n    const handleMouseDown = () => {\n      setIsClicking(true);\n    };\n\n    const handleMouseUp = () => {\n      setIsClicking(false);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseenter', handleMouseEnter, true);\n    document.addEventListener('mouseleave', handleMouseLeave, true);\n    document.addEventListener('mousedown', handleMouseDown);\n    document.addEventListener('mouseup', handleMouseUp);\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseenter', handleMouseEnter, true);\n      document.removeEventListener('mouseleave', handleMouseLeave, true);\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, []);\n\n  return (\n    <>\n      {/* eslint-disable-next-line react/forbid-dom-props */}\n      <div\n        className={`cursor-follower ${isHovering ? 'hovering' : ''} ${isClicking ? 'clicking' : ''}`}\n        style={{\n          left: mousePosition.x,\n          top: mousePosition.y,\n        }}\n      >\n        <div className=\"cursor-inner\"></div>\n      </div>\n      {/* eslint-disable-next-line react/forbid-dom-props */}\n      <div\n        className=\"cursor-trail\"\n        style={{\n          left: mousePosition.x,\n          top: mousePosition.y,\n        }}\n      ></div>\n    </>\n  );\n};\n\nexport default CursorFollower;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAC;IAAEU,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAMe,eAAe,GAAIC,CAAa,IAAK;MACzCR,gBAAgB,CAAC;QAAEC,CAAC,EAAEO,CAAC,CAACC,OAAO;QAAEP,CAAC,EAAEM,CAAC,CAACE;MAAQ,CAAC,CAAC;IAClD,CAAC;IAED,MAAMC,gBAAgB,GAAIH,CAAa,IAAK;MAC1C,MAAMI,MAAM,GAAGJ,CAAC,CAACI,MAAqB;MACtC,IAAIA,MAAM,CAACC,OAAO,CAAC,wCAAwC,CAAC,EAAE;QAC5DT,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC;IAED,MAAMU,gBAAgB,GAAIN,CAAa,IAAK;MAC1C,MAAMI,MAAM,GAAGJ,CAAC,CAACI,MAAqB;MACtC,IAAIA,MAAM,CAACC,OAAO,CAAC,wCAAwC,CAAC,EAAE;QAC5DT,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAED,MAAMW,eAAe,GAAGA,CAAA,KAAM;MAC5BT,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,MAAMU,aAAa,GAAGA,CAAA,KAAM;MAC1BV,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAEDW,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEX,eAAe,CAAC;IACvDU,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEP,gBAAgB,EAAE,IAAI,CAAC;IAC/DM,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEJ,gBAAgB,EAAE,IAAI,CAAC;IAC/DG,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEH,eAAe,CAAC;IACvDE,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEF,aAAa,CAAC;IAEnD,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEZ,eAAe,CAAC;MAC1DU,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAER,gBAAgB,EAAE,IAAI,CAAC;MAClEM,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAEL,gBAAgB,EAAE,IAAI,CAAC;MAClEG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEJ,eAAe,CAAC;MAC1DE,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtB,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBAEE1B,OAAA;MACE2B,SAAS,EAAE,mBAAmBlB,UAAU,GAAG,UAAU,GAAG,EAAE,IAAIE,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;MAC7FiB,KAAK,EAAE;QACLC,IAAI,EAAExB,aAAa,CAACE,CAAC;QACrBuB,GAAG,EAAEzB,aAAa,CAACG;MACrB,CAAE;MAAAkB,QAAA,eAEF1B,OAAA;QAAK2B,SAAS,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAENlC,OAAA;MACE2B,SAAS,EAAC,cAAc;MACxBC,KAAK,EAAE;QACLC,IAAI,EAAExB,aAAa,CAACE,CAAC;QACrBuB,GAAG,EAAEzB,aAAa,CAACG;MACrB;IAAE;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;AAAC9B,EAAA,CArEID,cAAwB;AAAAgC,EAAA,GAAxBhC,cAAwB;AAuE9B,eAAeA,cAAc;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}