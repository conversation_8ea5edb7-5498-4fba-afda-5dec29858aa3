.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  z-index: 1000;
  transition: all var(--duration-normal) var(--ease-out);
  border-bottom: 1px solid var(--gray-200);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-300);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 72px;
}

.logo h2 {
  font-family: 'Space Grotesk', sans-serif;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.025em;
  position: relative;
  transition: color var(--duration-normal) var(--ease-out);
}

.logo:hover h2 {
  color: var(--primary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.nav ul {
  display: flex;
  list-style: none;
  gap: var(--space-8);
}

.nav button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  position: relative;
  transition: all var(--duration-normal) var(--ease-out);
  letter-spacing: -0.011em;
}

.nav button:hover {
  color: var(--text-primary);
  background: var(--gray-100);
}

.nav button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: background var(--duration-normal) var(--ease-out);
}

.mobile-menu-toggle:hover {
  background: var(--gray-100);
}

.mobile-menu-toggle span {
  width: 24px;
  height: 2px;
  background: var(--text-secondary);
  margin: 3px 0;
  transition: all var(--duration-normal) var(--ease-out);
  border-radius: var(--radius-full);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header-container {
    padding: 0 var(--space-4);
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border-top: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-normal) var(--ease-out);
  }

  .nav.nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav ul {
    flex-direction: column;
    padding: var(--space-4) 0;
    gap: 0;
  }

  .nav button {
    padding: var(--space-4) var(--space-6);
    text-align: left;
    width: 100%;
    border-radius: 0;
    font-size: var(--text-base);
  }

  .nav button:hover {
    background: var(--gray-50);
  }

  .header-actions {
    gap: var(--space-2);
  }

  .mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
  }

  .mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
  }
}
