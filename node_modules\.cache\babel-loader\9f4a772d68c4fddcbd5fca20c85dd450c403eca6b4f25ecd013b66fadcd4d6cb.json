{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\ScrollProgress.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './ScrollProgress.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScrollProgress = () => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  useEffect(() => {\n    const handleScroll = () => {\n      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const progress = window.scrollY / totalHeight * 100;\n      setScrollProgress(progress);\n      setIsVisible(window.scrollY > 100);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-progress-bar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-progress-fill\",\n        style: {\n          width: `${scrollProgress}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: `scroll-to-top ${isVisible ? 'visible' : ''}`,\n      onClick: scrollToTop,\n      \"aria-label\": \"Scroll to top\",\n      type: \"button\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"24\",\n        height: \"24\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12 19V5M5 12L12 5L19 12\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-progress-ring\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"60\",\n          height: \"60\",\n          viewBox: \"0 0 60 60\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"30\",\n            cy: \"30\",\n            r: \"28\",\n            fill: \"none\",\n            stroke: \"rgba(255, 255, 255, 0.2)\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"30\",\n            cy: \"30\",\n            r: \"28\",\n            fill: \"none\",\n            stroke: \"url(#gradient)\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeDasharray: `${2 * Math.PI * 28}`,\n            strokeDashoffset: `${2 * Math.PI * 28 * (1 - scrollProgress / 100)}`,\n            transform: \"rotate(-90 30 30)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n            children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n              id: \"gradient\",\n              x1: \"0%\",\n              y1: \"0%\",\n              x2: \"100%\",\n              y2: \"0%\",\n              children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                offset: \"0%\",\n                stopColor: \"#667eea\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                offset: \"100%\",\n                stopColor: \"#764ba2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ScrollProgress, \"FejIHpCXBB29KNLTJCmJijLQ/x8=\");\n_c = ScrollProgress;\nexport default ScrollProgress;\nvar _c;\n$RefreshReg$(_c, \"ScrollProgress\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScrollProgress", "_s", "scrollProgress", "setScrollProgress", "isVisible", "setIsVisible", "handleScroll", "totalHeight", "document", "documentElement", "scrollHeight", "window", "innerHeight", "progress", "scrollY", "addEventListener", "removeEventListener", "scrollToTop", "scrollTo", "top", "behavior", "children", "className", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "PI", "strokeDashoffset", "transform", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/ScrollProgress.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ScrollProgress.css';\n\nconst ScrollProgress: React.FC = () => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const progress = (window.scrollY / totalHeight) * 100;\n      setScrollProgress(progress);\n      setIsVisible(window.scrollY > 100);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <>\n      {/* Scroll Progress Bar */}\n      <div className=\"scroll-progress-bar\">\n        {/* Inline style required for dynamic scroll percentage */}\n        <div\n          className=\"scroll-progress-fill\"\n          style={{ width: `${scrollProgress}%` }}\n        ></div>\n      </div>\n\n      {/* Scroll to Top Button */}\n      <button\n        className={`scroll-to-top ${isVisible ? 'visible' : ''}`}\n        onClick={scrollToTop}\n        aria-label=\"Scroll to top\"\n        type=\"button\"\n      >\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n          <path \n            d=\"M12 19V5M5 12L12 5L19 12\" \n            stroke=\"currentColor\" \n            strokeWidth=\"2\" \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\"\n          />\n        </svg>\n        <div className=\"scroll-progress-ring\">\n          <svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\">\n            <circle\n              cx=\"30\"\n              cy=\"30\"\n              r=\"28\"\n              fill=\"none\"\n              stroke=\"rgba(255, 255, 255, 0.2)\"\n              strokeWidth=\"2\"\n            />\n            <circle\n              cx=\"30\"\n              cy=\"30\"\n              r=\"28\"\n              fill=\"none\"\n              stroke=\"url(#gradient)\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeDasharray={`${2 * Math.PI * 28}`}\n              strokeDashoffset={`${2 * Math.PI * 28 * (1 - scrollProgress / 100)}`}\n              transform=\"rotate(-90 30 30)\"\n            />\n            <defs>\n              <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                <stop offset=\"0%\" stopColor=\"#667eea\" />\n                <stop offset=\"100%\" stopColor=\"#764ba2\" />\n              </linearGradient>\n            </defs>\n          </svg>\n        </div>\n      </button>\n    </>\n  );\n};\n\nexport default ScrollProgress;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,WAAW,GAAGC,QAAQ,CAACC,eAAe,CAACC,YAAY,GAAGC,MAAM,CAACC,WAAW;MAC9E,MAAMC,QAAQ,GAAIF,MAAM,CAACG,OAAO,GAAGP,WAAW,GAAI,GAAG;MACrDJ,iBAAiB,CAACU,QAAQ,CAAC;MAC3BR,YAAY,CAACM,MAAM,CAACG,OAAO,GAAG,GAAG,CAAC;IACpC,CAAC;IAEDH,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAET,YAAY,CAAC;IAC/C,OAAO,MAAMK,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEV,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBN,MAAM,CAACO,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACEvB,OAAA,CAAAE,SAAA;IAAAsB,QAAA,gBAEExB,OAAA;MAAKyB,SAAS,EAAC,qBAAqB;MAAAD,QAAA,eAElCxB,OAAA;QACEyB,SAAS,EAAC,sBAAsB;QAChCC,KAAK,EAAE;UAAEC,KAAK,EAAE,GAAGtB,cAAc;QAAI;MAAE;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN/B,OAAA;MACEyB,SAAS,EAAE,iBAAiBlB,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;MACzDyB,OAAO,EAAEZ,WAAY;MACrB,cAAW,eAAe;MAC1Ba,IAAI,EAAC,QAAQ;MAAAT,QAAA,gBAEbxB,OAAA;QAAK2B,KAAK,EAAC,IAAI;QAACO,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAAAZ,QAAA,eACzDxB,OAAA;UACEqC,CAAC,EAAC,0BAA0B;UAC5BC,MAAM,EAAC,cAAc;UACrBC,WAAW,EAAC,GAAG;UACfC,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/B,OAAA;QAAKyB,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnCxB,OAAA;UAAK2B,KAAK,EAAC,IAAI;UAACO,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAAAX,QAAA,gBAC7CxB,OAAA;YACE0C,EAAE,EAAC,IAAI;YACPC,EAAE,EAAC,IAAI;YACPC,CAAC,EAAC,IAAI;YACNR,IAAI,EAAC,MAAM;YACXE,MAAM,EAAC,0BAA0B;YACjCC,WAAW,EAAC;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACF/B,OAAA;YACE0C,EAAE,EAAC,IAAI;YACPC,EAAE,EAAC,IAAI;YACPC,CAAC,EAAC,IAAI;YACNR,IAAI,EAAC,MAAM;YACXE,MAAM,EAAC,gBAAgB;YACvBC,WAAW,EAAC,GAAG;YACfC,aAAa,EAAC,OAAO;YACrBK,eAAe,EAAE,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,EAAE,EAAG;YACvCC,gBAAgB,EAAE,GAAG,CAAC,GAAGF,IAAI,CAACC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG1C,cAAc,GAAG,GAAG,CAAC,EAAG;YACrE4C,SAAS,EAAC;UAAmB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACF/B,OAAA;YAAAwB,QAAA,eACExB,OAAA;cAAgBkD,EAAE,EAAC,UAAU;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,MAAM;cAACC,EAAE,EAAC,IAAI;cAAA9B,QAAA,gBAC7DxB,OAAA;gBAAMuD,MAAM,EAAC,IAAI;gBAACC,SAAS,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxC/B,OAAA;gBAAMuD,MAAM,EAAC,MAAM;gBAACC,SAAS,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAAC3B,EAAA,CAnFID,cAAwB;AAAAsD,EAAA,GAAxBtD,cAAwB;AAqF9B,eAAeA,cAAc;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}