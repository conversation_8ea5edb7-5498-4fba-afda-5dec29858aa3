{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\portfolio\\\\src\\\\components\\\\LoadingScreen.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './LoadingScreen.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingScreen = ({\n  onLoadingComplete\n}) => {\n  _s();\n  const [progress, setProgress] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setProgress(prevProgress => {\n        if (prevProgress >= 100) {\n          clearInterval(timer);\n          setIsComplete(true);\n          setTimeout(() => {\n            onLoadingComplete();\n          }, 800);\n          return 100;\n        }\n        return prevProgress + Math.random() * 15;\n      });\n    }, 100);\n    return () => clearInterval(timer);\n  }, [onLoadingComplete]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `loading-screen ${isComplete ? 'fade-out' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Saran\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-underline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-progress\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\",\n            style: {\n              width: `${Math.min(progress, 100)}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-text\",\n          children: [Math.round(Math.min(progress, 100)), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-subtitle\",\n        children: \"Crafting Digital Experiences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-particles\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `particle particle-${i + 1}`\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(LoadingScreen, \"aWKCuD/IFqH8AjkN5xDive3KztM=\");\n_c = LoadingScreen;\nexport default LoadingScreen;\nvar _c;\n$RefreshReg$(_c, \"LoadingScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "LoadingScreen", "onLoadingComplete", "_s", "progress", "setProgress", "isComplete", "setIsComplete", "timer", "setInterval", "prevProgress", "clearInterval", "setTimeout", "Math", "random", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "min", "round", "Array", "map", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/portfolio/src/components/LoadingScreen.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './LoadingScreen.css';\n\ninterface LoadingScreenProps {\n  onLoadingComplete: () => void;\n}\n\nconst LoadingScreen: React.FC<LoadingScreenProps> = ({ onLoadingComplete }) => {\n  const [progress, setProgress] = useState(0);\n  const [isComplete, setIsComplete] = useState(false);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setProgress((prevProgress) => {\n        if (prevProgress >= 100) {\n          clearInterval(timer);\n          setIsComplete(true);\n          setTimeout(() => {\n            onLoadingComplete();\n          }, 800);\n          return 100;\n        }\n        return prevProgress + Math.random() * 15;\n      });\n    }, 100);\n\n    return () => clearInterval(timer);\n  }, [onLoadingComplete]);\n\n  return (\n    <div className={`loading-screen ${isComplete ? 'fade-out' : ''}`}>\n      <div className=\"loading-content\">\n        <div className=\"loading-logo\">\n          <h1>Saran</h1>\n          <div className=\"logo-underline\"></div>\n        </div>\n        \n        <div className=\"loading-progress\">\n          <div className=\"progress-bar\">\n            <div \n              className=\"progress-fill\"\n              style={{ width: `${Math.min(progress, 100)}%` }}\n            ></div>\n          </div>\n          <div className=\"progress-text\">\n            {Math.round(Math.min(progress, 100))}%\n          </div>\n        </div>\n\n        <div className=\"loading-subtitle\">\n          Crafting Digital Experiences\n        </div>\n      </div>\n\n      <div className=\"loading-particles\">\n        {[...Array(20)].map((_, i) => (\n          <div key={i} className={`particle particle-${i + 1}`}></div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAMU,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BJ,WAAW,CAAEK,YAAY,IAAK;QAC5B,IAAIA,YAAY,IAAI,GAAG,EAAE;UACvBC,aAAa,CAACH,KAAK,CAAC;UACpBD,aAAa,CAAC,IAAI,CAAC;UACnBK,UAAU,CAAC,MAAM;YACfV,iBAAiB,CAAC,CAAC;UACrB,CAAC,EAAE,GAAG,CAAC;UACP,OAAO,GAAG;QACZ;QACA,OAAOQ,YAAY,GAAGG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;MAC1C,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMH,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACN,iBAAiB,CAAC,CAAC;EAEvB,oBACEF,OAAA;IAAKe,SAAS,EAAE,kBAAkBT,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;IAAAU,QAAA,gBAC/DhB,OAAA;MAAKe,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BhB,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAAgB,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdpB,OAAA;UAAKe,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhB,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BhB,OAAA;YACEe,SAAS,EAAC,eAAe;YACzBM,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAGT,IAAI,CAACU,GAAG,CAACnB,QAAQ,EAAE,GAAG,CAAC;YAAI;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BH,IAAI,CAACW,KAAK,CAACX,IAAI,CAACU,GAAG,CAACnB,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAC,GACvC;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/B,CAAC,GAAGS,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB5B,OAAA;QAAae,SAAS,EAAE,qBAAqBa,CAAC,GAAG,CAAC;MAAG,GAA3CA,CAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgD,CAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAtDIF,aAA2C;AAAA4B,EAAA,GAA3C5B,aAA2C;AAwDjD,eAAeA,aAAa;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}